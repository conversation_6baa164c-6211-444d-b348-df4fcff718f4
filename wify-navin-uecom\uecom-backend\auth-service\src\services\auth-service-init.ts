/**
 * Auth Service Initialization
 *
 * This file initializes the auth service singleton instance.
 * It's separated from the auth.ts file to avoid circular dependencies.
 *
 * @module services/auth-service-init
 */

import { AuthService, setAuthServiceInstance } from './auth';
import { AuthStrategyFactory } from '../factories/auth-strategy-factory';
import { logger } from '../utils/logger';

// Initialize the auth service with the default strategy
export function initAuthService(): void {
    try {
        const strategy = AuthStrategyFactory.getDefaultStrategy();
        const authServiceInstance = new AuthService(strategy);
        setAuthServiceInstance(authServiceInstance);
    } catch (error) {
        logger.error('Failed to initialize auth service:', error);
    }
}
