/**
 * Redis Repository
 *
 * This repository provides an abstraction over Redis operations.
 * It follows the Repository pattern to provide a clean interface for data access.
 *
 * @module repositories/redis
 */

import Redis from 'ioredis';
import { IConfigService } from '../interfaces/config-service.interface';
import { ConfigService } from '../configs/config.service';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';

export interface IRedisRepository {
    /**
     * Checks if the Redis client is connected
     *
     * @returns {Promise<boolean>} True if connected, false otherwise
     */
    isReady(): Promise<boolean>;

    /**
     * Sets a key-value pair in Redis with an optional expiration time
     *
     * @param {string} key - The key to set
     * @param {string} value - The value to set
     * @param {number} [expirySeconds] - Optional expiration time in seconds
     * @returns {Promise<string>} The result of the operation
     */
    set(key: string, value: string, expirySeconds?: number): Promise<string>;

    /**
     * Gets a value from Redis by key
     *
     * @param {string} key - The key to get
     * @returns {Promise<string | null>} The value or null if not found
     */
    get(key: string): Promise<string | null>;

    /**
     * Deletes a key from Redis
     *
     * @param {string} key - The key to delete
     * @returns {Promise<number>} The number of keys deleted
     */
    del(key: string): Promise<number>;

    /**
     * Closes the Redis connection
     *
     * @returns {Promise<void>}
     */
    close(): Promise<void>;
}

export class RedisRepository implements IRedisRepository {
    private readonly redis: Redis;
    private readonly configService: IConfigService;
    private isConnected: boolean = false;

    constructor(configService: IConfigService) {
        this.configService = configService;

        // Get Redis configuration from environment variables or use defaults
        const redisUrl = this.configService.getRedisUrl();
        const nodeEnv = this.configService.getNodeEnv();

        // Create Redis client with appropriate configuration
        if (nodeEnv === 'production') {
            // Production configuration with full Redis connection
            this.redis = new Redis(redisUrl, {
                // Reconnect strategy
                retryStrategy: (times) => {
                    const delay = Math.min(times * 50, 2000);
                    return delay;
                },
                // Connection timeout
                connectTimeout: 10000,
                // Enable auto-reconnect
                autoResubscribe: true,
                autoResendUnfulfilledCommands: true,
                // Maximum number of reconnection attempts
                maxRetriesPerRequest: 5,
            });
        } else {
            // Development/testing configuration
            // Use Redis if available, but don't fail if not
            try {
                this.redis = new Redis(redisUrl, {
                    // Less aggressive reconnection for development
                    retryStrategy: (times) => {
                        // Only retry a few times in development
                        if (times > 3) return null; // Stop retrying after 3 attempts
                        const delay = Math.min(times * 100, 3000);
                        return delay;
                    },
                    // Shorter connection timeout for development
                    connectTimeout: 5000,
                    // Enable auto-reconnect
                    autoResubscribe: true,
                    autoResendUnfulfilledCommands: true,
                    // Fewer retries in development
                    maxRetriesPerRequest: 2,
                });
                console.log('🔌 Connected to Redis in development mode');
            } catch (error) {
                console.warn(
                    '⚠️ Could not connect to Redis in development mode. Some features may not work properly.'
                );
                // Create a minimal Redis client that won't throw errors
                // This allows the application to run even without Redis in development
                this.redis = new Redis();
            }
        }

        // Set up event handlers
        this.setupEventHandlers();
    }

    /**
     * Sets up event handlers for the Redis client
     *
     * @private
     */
    private setupEventHandlers(): void {
        // Connection successful
        this.redis.on('connect', () => {
            console.log('🔌 Connected to Redis');
            this.isConnected = true;
        });

        // Connection ready
        this.redis.on('ready', () => {
            console.log('✅ Redis connection is ready');
        });

        // Connection error
        this.redis.on('error', (err) => {
            console.error('❌ Redis connection error:', err);
            this.isConnected = false;
        });

        // Connection closed
        this.redis.on('close', () => {
            console.log('🔌 Redis connection closed');
            this.isConnected = false;
        });

        // Reconnecting
        this.redis.on('reconnecting', () => {
            console.log('🔄 Reconnecting to Redis...');
        });
    }

    /**
     * Checks if the Redis client is connected
     *
     * @returns {Promise<boolean>} True if connected, false otherwise
     */
    async isReady(): Promise<boolean> {
        try {
            // Try to ping Redis to check if it's really connected
            const pong = await this.redis.ping();
            return pong === 'PONG';
        } catch (error) {
            console.error('❌ Redis ping failed:', error);
            return false;
        }
    }

    /**
     * Sets a key-value pair in Redis with an optional expiration time
     *
     * @param {string} key - The key to set
     * @param {string} value - The value to set
     * @param {number} [expirySeconds] - Optional expiration time in seconds
     * @returns {Promise<string>} The result of the operation
     */
    async set(
        key: string,
        value: string,
        expirySeconds?: number
    ): Promise<string> {
        if (expirySeconds) {
            return this.redis.set(key, value, 'EX', expirySeconds);
        }
        return this.redis.set(key, value);
    }

    /**
     * Gets a value from Redis by key
     *
     * @param {string} key - The key to get
     * @returns {Promise<string | null>} The value or null if not found
     */
    async get(key: string): Promise<string | null> {
        return this.redis.get(key);
    }

    /**
     * Deletes a key from Redis
     *
     * @param {string} key - The key to delete
     * @returns {Promise<number>} The number of keys deleted
     */
    async del(key: string): Promise<number> {
        return this.redis.del(key);
    }

    /**
     * Closes the Redis connection
     *
     * @returns {Promise<void>}
     */
    async close(): Promise<void> {
        await this.redis.quit();
        this.isConnected = false;
        console.log('🔌 Redis connection closed');
    }
}

// Factory function for creating a Redis repository
export const createRedisRepository = (): IRedisRepository => {
    const configService = container.resolve<IConfigService>(
        SERVICE_TOKENS.CONFIG_SERVICE
    );
    return new RedisRepository(configService);
};
