/**
 * JWT Service
 *
 * This service handles JWT token generation and verification.
 * It provides methods for generating access, refresh, and session tokens.
 *
 * @module services/jwt
 */

import jwt from 'jsonwebtoken';
import { ConfigService } from '../configs/config.service';
import { logger } from '../utils/logger';

// Token types
export enum TokenType {
    ACCESS = 'access',
    REFRESH = 'refresh',
    SESSION = 'session',
}

// Token payload interface
export interface TokenPayload {
    sub: string; // Subject (user ID)
    type: TokenType; // Token type
    jti?: string; // JWT ID (unique identifier for this token)
    [key: string]: any; // Allow additional properties
}

export class JwtService {
    private readonly configService: ConfigService;

    // Default token expiration times
    private readonly DEFAULT_ACCESS_TOKEN_EXPIRY = '1h'; // 1 hour
    private readonly DEFAULT_REFRESH_TOKEN_EXPIRY = '7d'; // 7 days

    constructor() {
        this.configService = new ConfigService();
    }

    /**
     * Generates a unique token ID
     *
     * @private
     * @returns {string} A unique token ID
     */
    private generateTokenId(): string {
        return (
            Date.now().toString(36) + Math.random().toString(36).substring(2)
        );
    }

    /**
     * Generates a JWT access token for a user
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT access token
     */
    generateAccessToken(userId: string): string {
        try {
            const secret = this.configService.getJwtSecret();
            const payload = {
                sub: userId,
                type: TokenType.ACCESS,
                jti: this.generateTokenId(),
                // Add expiry directly in the payload
                exp: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour
            };

            // Use a simple approach without options to avoid TypeScript issues
            const token = jwt.sign(payload, secret);
            return token;
        } catch (error) {
            logger.error('Error generating access token:', error);
            throw new Error('Failed to generate access token');
        }
    }

    /**
     * Generates a JWT refresh token for a user
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT refresh token
     */
    generateRefreshToken(userId: string): string {
        try {
            const secret = this.configService.getJwtSecret();
            const payload = {
                sub: userId,
                type: TokenType.REFRESH,
                jti: this.generateTokenId(),
                // Add expiry directly in the payload
                exp: Math.floor(Date.now() / 1000) + 7 * 24 * 60 * 60, // 7 days
            };

            // Use a simple approach without options to avoid TypeScript issues
            const token = jwt.sign(payload, secret);
            return token;
        } catch (error) {
            logger.error('Error generating refresh token:', error);
            throw new Error('Failed to generate refresh token');
        }
    }

    /**
     * Generates a JWT session token for a user
     * This token is meant to be stored in a secure HTTP-only cookie
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT session token
     */
    generateSessionToken(userId: string): string {
        try {
            const secret = this.configService.getJwtSecret();
            const expiryHours = this.configService.getJwtSessionExpiryHours();

            const payload = {
                sub: userId,
                type: TokenType.SESSION,
                jti: this.generateTokenId(),
                // Add expiry directly in the payload
                exp: Math.floor(Date.now() / 1000) + expiryHours * 60 * 60,
            };

            // Use a simple approach without options to avoid TypeScript issues
            const token = jwt.sign(payload, secret);
            return token;
        } catch (error) {
            logger.error('Error generating session token:', error);
            throw new Error('Failed to generate session token');
        }
    }

    /**
     * Verifies a JWT token and returns the decoded payload
     *
     * @param {string} token - The JWT token to verify
     * @param {TokenType} expectedType - The expected token type
     * @returns {TokenPayload} The decoded token payload
     * @throws {Error} If the token is invalid or has expired
     */
    verifyToken(token: string, expectedType?: TokenType): TokenPayload {
        try {
            const secret = this.configService.getJwtSecret();
            const decoded = jwt.verify(token, secret) as TokenPayload;

            // Check if the token type matches the expected type
            if (expectedType && decoded.type !== expectedType) {
                throw new Error('Invalid token type');
            }

            return decoded;
        } catch (error: any) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Token has expired');
            }
            throw new Error('Invalid token');
        }
    }
}

// Export a singleton instance
export const jwtService = new JwtService();
