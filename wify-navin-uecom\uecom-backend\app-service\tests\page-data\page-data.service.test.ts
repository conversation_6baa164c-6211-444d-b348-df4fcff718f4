import { pageDataService } from '../../src/services/page-data';
import { pageData } from '../../src/data/pageData';

describe('Page Data Service', () => {
    describe('getPageData', () => {
        it('should return page data for the specified page', async () => {
            const result = await pageDataService.getPageData('home');
            expect(result).toEqual(pageData);
        });

        it('should return a valid PageData object', async () => {
            const result = await pageDataService.getPageData('home');
            expect(result).toHaveProperty('sections');
            expect(Array.isArray(result.sections)).toBe(true);
        });
    });

    describe('getSectionsByType', () => {
        it('should return sections matching the specified type', () => {
            const sections = pageData.sections;
            const headerSections = pageDataService.getSectionsByType(sections, 'header');
            
            expect(headerSections.length).toBeGreaterThan(0);
            expect(headerSections.every(section => section.type === 'header')).toBe(true);
        });

        it('should return an empty array if no sections match the type', () => {
            const sections = pageData.sections;
            const nonExistentSections = pageDataService.getSectionsByType(sections, 'non-existent');
            
            expect(nonExistentSections).toEqual([]);
        });
    });

    describe('getHeaderSection', () => {
        it('should return the header section if it exists', () => {
            const sections = pageData.sections;
            const headerSection = pageDataService.getHeaderSection(sections);
            
            expect(headerSection).toBeDefined();
            expect(headerSection?.type).toBe('header');
        });

        it('should return undefined if no header section exists', () => {
            const sections = pageData.sections.filter(section => section.type !== 'header');
            const headerSection = pageDataService.getHeaderSection(sections);
            
            expect(headerSection).toBeUndefined();
        });
    });

    describe('getFooterSection', () => {
        it('should return the footer section if it exists', () => {
            const sections = pageData.sections;
            const footerSection = pageDataService.getFooterSection(sections);
            
            expect(footerSection).toBeDefined();
            expect(footerSection?.type).toBe('footer');
        });

        it('should return undefined if no footer section exists', () => {
            const sections = pageData.sections.filter(section => section.type !== 'footer');
            const footerSection = pageDataService.getFooterSection(sections);
            
            expect(footerSection).toBeUndefined();
        });
    });
});
