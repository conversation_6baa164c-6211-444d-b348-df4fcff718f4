import request from 'supertest';
import express from 'express';
import authRouter from '../../src/routes/auth';

const app = express();
app.use(express.json());
app.use('/auth', authRouter);

describe('Auth Routes', () => {
    let server;

    beforeAll((done) => {
        server = app.listen(0, () => done());
    });

    afterAll((done) => {
        if (server) {
            server.close(done);
        }
    });

    describe('GET /auth/', () => {
        it('should return health check status', async () => {
            const response = await request(server).get('/auth/');
            expect(response.status).toBe(200);
            expect(response.body).toEqual({});
        });
    });

    describe('POST /auth/request-otp', () => {
        it('should return 400 for invalid mobile number', async () => {
            const response = await request(server)
                .post('/auth/request-otp')
                .send({ mobile: '123' });
            expect(response.status).toBe(400);
        });

        it('should return 200 for valid mobile number', async () => {
            const response = await request(server)
                .post('/auth/request-otp')
                .send({ mobile: '**********' });
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty('message');
            expect(response.body).toHaveProperty('mobile');
            expect(response.body).toHaveProperty('otp');
        });
    });

    describe('POST /auth/verify-otp', () => {
        it('should return 400 for invalid OTP format', async () => {
            const response = await request(server)
                .post('/auth/verify-otp')
                .send({
                    mobile: '**********',
                    otp: '12345',
                });
            expect(response.status).toBe(400);
        });

        it('should return 400 for invalid mobile number', async () => {
            const response = await request(server)
                .post('/auth/verify-otp')
                .send({
                    mobile: '123',
                    otp: '123456',
                });
            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile or OTP' });
        });

        it('should return 200 with error for incorrect OTP', async () => {
            const response = await request(server)
                .post('/auth/verify-otp')
                .send({
                    mobile: '**********',
                    otp: '999999',
                });
            expect(response.status).toBe(200);
            expect(response.body).toHaveProperty(
                'error',
                'Invalid OTP or mobile number'
            );
        });
    });

    describe('POST /auth/logout', () => {
        it('should return 200 and clear the refresh token cookie', async () => {
            const response = await request(server).post('/auth/logout');

            expect(response.status).toBe(200);
            expect(response.body.message).toBe('Logged out successfully');

            // Check for Set-Cookie header that clears the cookie
            const cookies = response.headers['set-cookie'] || [];

            // In test environment, the cookie might not be set since we're not in production mode
            // but we should still get a successful response
            expect(response.body.statusCode).toBe(200);
        });
    });
});
