import { ISmsAdapter } from './sms-adapter.interface';
import { SmsRequest } from '../types/auth.types';
import { APIResponse } from '../types/response.types';
import { IConfigService } from '../interfaces/config-service.interface';
import { buildExtApiResponse } from '../utils/apiResponse';
import { StatusCodes } from 'http-status-codes';
import { CustomHttpService } from '../services/http';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';
import { EnvironmentUtils } from '../utils/environment.utils';

/**
 * MTalkz API response formats
 */
interface MtalkzV1Response {
    [key: string]: {
        id?: string;
        mobile?: string;
        status?: string;
    };
}

interface MtalkzV2Response {
    status?: string;
    type?: string;
    message?: string;
    data?: any;
    Details?: string;
    Status?: string;
}

type MtalkzResponse = MtalkzV1Response | MtalkzV2Response;

/**
 * MTalkz SMS Adapter
 *
 * Implements the SMS adapter interface for the MTalkz SMS gateway.
 */
export class MtalkzSmsAdapter implements ISmsAdapter {
    private readonly configService: IConfigService;
    private readonly httpService: CustomHttpService;

    // Default endpoint for MTalkz API
    private readonly DEFAULT_ENDPOINT =
        'https://msg.mtalkz.com/V2/http-api.php';

    // Timeout for API requests in milliseconds
    private readonly REQUEST_TIMEOUT = 5000;

    constructor(configService: IConfigService) {
        this.configService = configService;
        this.httpService = container.resolve<CustomHttpService>(
            SERVICE_TOKENS.HTTP_SERVICE
        );
    }

    /**
     * Masks a phone number for logging purposes
     */
    private maskPhoneNumber(phoneNumber: string): string {
        return phoneNumber.length <= 4
            ? '****'
            : '*'.repeat(phoneNumber.length - 4) + phoneNumber.slice(-4);
    }

    /**
     * Validates SMS content for security and compliance
     */
    private validateSmsContent(message: string): boolean {
        // Check if message is empty or too long
        if (!message || message.trim().length === 0 || message.length > 160) {
            return false;
        }

        // Check for potentially malicious content
        const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /data:/i,
            /onerror=/i,
            /onclick=/i,
        ];

        return !suspiciousPatterns.some((pattern) => pattern.test(message));
    }

    /**
     * Validates the phone number format
     */
    private validatePhoneNumber(phoneNumber: string): boolean {
        return Boolean(phoneNumber && /^\d{10,15}$/.test(phoneNumber));
    }

    /**
     * Creates a URL for the MTalkz API with proper encoding
     */
    private buildApiUrl(
        endpoint: string,
        phoneNumber: string,
        message: string,
        apiKey: string,
        senderId: string
    ): string {
        // Use URLSearchParams to properly encode parameters
        const params = new URLSearchParams();
        params.append('apikey', apiKey);
        params.append('senderid', senderId);
        params.append('number', phoneNumber);
        params.append('message', message);

        return `${endpoint}?${params.toString()}`;
    }

    /**
     * Sends an SMS request to the MTalkz API
     */
    private async sendSmsRequest(
        phoneNumber: string,
        message: string,
        apiKey: string,
        senderId: string
    ): Promise<MtalkzResponse> {
        const url = this.buildApiUrl(
            this.DEFAULT_ENDPOINT,
            phoneNumber,
            message,
            apiKey,
            senderId
        );

        try {
            return await this.httpService.get(url, {
                timeout: this.REQUEST_TIMEOUT,
            });
        } catch (error: any) {
            console.error(`⚠️ Error sending SMS to MTalkz API:`, error.message);
            throw error;
        }
    }

    /**
     * Processes a V1 format response from MTalkz API
     */
    private processV1Response(response: MtalkzV1Response): APIResponse {
        const numericKeys = Object.keys(response).filter(
            (key) => !isNaN(Number(key))
        );

        if (numericKeys.length === 0) {
            return buildExtApiResponse(
                StatusCodes.OK,
                'SMS request processed',
                response,
                null
            );
        }

        const messageInfo = response[numericKeys[0]];

        if (!messageInfo || !messageInfo.status) {
            return buildExtApiResponse(
                StatusCodes.OK,
                'SMS request processed',
                response,
                null
            );
        }

        if (['SUBMITTED', 'DELIVERED'].includes(messageInfo.status)) {
            return buildExtApiResponse(
                StatusCodes.OK,
                'SMS sent successfully',
                messageInfo,
                null
            );
        }

        return buildExtApiResponse(
            StatusCodes.OK,
            'SMS request processed',
            response,
            null
        );
    }

    /**
     * Processes a V2 format response from MTalkz API
     */
    private processV2Response(response: MtalkzV2Response): APIResponse {
        // Check for error response
        if (response.Status === 'Error' && response.Details) {
            return buildExtApiResponse(
                StatusCodes.BAD_REQUEST,
                response.Details,
                null,
                { code: 'SMS_GATEWAY_ERROR', details: response }
            );
        }

        // Check for success response
        if (response.status === 'success' || response.type === 'success') {
            return buildExtApiResponse(
                StatusCodes.OK,
                response.message || 'SMS sent successfully',
                response.data || response,
                null
            );
        }

        // Default case - assume success if no clear error
        return buildExtApiResponse(
            StatusCodes.OK,
            'SMS request processed',
            response,
            null
        );
    }

    /**
     * Determines the response format and processes accordingly
     */
    private processResponse(responseData: MtalkzResponse): APIResponse {
        // Check if it's a V2 format response
        if (
            'Status' in responseData ||
            'status' in responseData ||
            'type' in responseData
        ) {
            return this.processV2Response(responseData as MtalkzV2Response);
        }

        // Otherwise, treat as V1 format
        return this.processV1Response(responseData as MtalkzV1Response);
    }

    /**
     * Sends an SMS using the MTalkz SMS gateway
     * In non-production environments, skips actual SMS sending and returns a success response
     *
     * @param {SmsRequest} request - Object containing recipient phone number and message text
     * @returns {Promise<APIResponse>} A structured API response indicating success or failure
     */
    async sendSMS({ to, message }: SmsRequest): Promise<APIResponse> {
        // Validate phone number
        if (!this.validatePhoneNumber(to)) {
            return buildExtApiResponse(
                StatusCodes.BAD_REQUEST,
                'Invalid phone number format',
                null,
                { code: 'INVALID_PHONE_NUMBER' }
            );
        }

        // Validate message content
        if (!this.validateSmsContent(message)) {
            return buildExtApiResponse(
                StatusCodes.BAD_REQUEST,
                'Invalid SMS content',
                null,
                { code: 'INVALID_SMS_CONTENT' }
            );
        }

        // Extract last 10 digits for Indian phone numbers
        const phoneNumber = to.slice(-10);

        // In non-production environments, skip actual SMS sending
        if (!EnvironmentUtils.isProduction()) {
            console.log(
                `🔧 [${EnvironmentUtils.getEnvironment()}] Skipping actual SMS sending to: ${this.maskPhoneNumber(phoneNumber)}`
            );
            console.log(`🔧 SMS content: ${message}`);

            // Return a mock success response
            return buildExtApiResponse(
                StatusCodes.OK,
                'SMS sent successfully (simulated in non-production environment)',
                {
                    id: `mock-${Date.now()}`,
                    mobile: phoneNumber,
                    status: 'SIMULATED',
                    environment: EnvironmentUtils.getEnvironment(),
                },
                null
            );
        }

        // For production, proceed with actual SMS sending
        // Get API credentials
        const apiKey = this.configService.getMtalkzApiKey();
        const senderId = this.configService.getMtalkzSenderId();

        // Log with masked data for security
        console.log('📤 Sending SMS to:', this.maskPhoneNumber(phoneNumber));
        console.log(
            '📤 Using API key:',
            apiKey.slice(0, 4) + '...' + apiKey.slice(-4)
        );

        try {
            // Send the SMS request
            const response = await this.sendSmsRequest(
                phoneNumber,
                message,
                apiKey,
                senderId
            );

            // Process the response
            return this.processResponse(response);
        } catch (error: any) {
            // Handle any errors
            return buildExtApiResponse(
                StatusCodes.BAD_REQUEST,
                'SMS sending failed',
                null,
                {
                    code: 'SMS_GATEWAY_ERROR',
                    message: error.message,
                }
            );
        }
    }
}

/**
 * Factory function for creating an MTalkz SMS adapter
 */
export const createMtalkzSmsAdapter = (
    configService: IConfigService
): ISmsAdapter => {
    return new MtalkzSmsAdapter(configService);
};
