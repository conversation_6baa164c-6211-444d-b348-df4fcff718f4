import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import ErrorPage from './error'

// Mock Next.js Link component
jest.mock('next/link', () => {
    return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
        return <a href={href}>{children}</a>
    }
})

describe('ErrorPage', () => {
    const mockError = new Error('Test error message')
    const mockReset = jest.fn()

    beforeEach(() => {
        jest.clearAllMocks()
        // Mock console.error to avoid noise in tests
        jest.spyOn(console, 'error').mockImplementation(() => {})
    })

    afterEach(() => {
        jest.restoreAllMocks()
    })

    it('renders error page with correct title and message', () => {
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        expect(screen.getByText('Something went wrong')).toBeInTheDocument()
        expect(screen.getByText(/We're sorry, but there was an unexpected error/)).toBeInTheDocument()
    })

    it('calls reset function when Try Again button is clicked', () => {
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        const tryAgainButton = screen.getByText('Try Again')
        fireEvent.click(tryAgainButton)
        
        expect(mockReset).toHaveBeenCalledTimes(1)
    })

    it('shows error details in development mode', () => {
        const originalEnv = process.env.NODE_ENV
        process.env.NODE_ENV = 'development'
        
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        expect(screen.getByText('Error Details:')).toBeInTheDocument()
        expect(screen.getByText('Test error message')).toBeInTheDocument()
        
        process.env.NODE_ENV = originalEnv
    })

    it('hides error details in production mode', () => {
        const originalEnv = process.env.NODE_ENV
        process.env.NODE_ENV = 'production'
        
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        expect(screen.queryByText('Error Details:')).not.toBeInTheDocument()
        expect(screen.queryByText('Test error message')).not.toBeInTheDocument()
        
        process.env.NODE_ENV = originalEnv
    })

    it('renders home link correctly', () => {
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        const homeLink = screen.getByText('Go Home')
        expect(homeLink.closest('a')).toHaveAttribute('href', '/')
    })

    it('renders support link correctly', () => {
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        const supportLink = screen.getByText('contact support')
        expect(supportLink.closest('a')).toHaveAttribute('href', '/help')
    })

    it('logs error to console on mount', () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
        
        render(<ErrorPage error={mockError} reset={mockReset} />)
        
        expect(consoleSpy).toHaveBeenCalledWith('Application error:', mockError)
    })
})
