/**
 * Authentication Strategy Interface
 *
 * This interface defines the contract for different authentication strategies.
 * It follows the Strategy pattern to allow for different authentication methods.
 *
 * @module interfaces/auth-strategy
 */

import {
    OtpRequest,
    OtpVerificationRequest,
    ServiceResponse,
    OtpServiceResponse,
    VerificationResponse,
} from '../types/auth.types';

export interface IAuthStrategy {
    /**
     * Initiates the authentication process
     *
     * @param {OtpRequest} params - Object containing authentication parameters
     * @returns {Promise<ServiceResponse<OtpServiceResponse>>} Response with success status and data or error
     */
    initiate(params: OtpRequest): Promise<ServiceResponse<OtpServiceResponse>>;

    /**
     * Verifies the authentication
     *
     * @param {OtpVerificationRequest} params - Object containing verification parameters
     * @returns {Promise<ServiceResponse<VerificationResponse>>} Response with verification result
     */
    verify(
        params: OtpVerificationRequest
    ): Promise<ServiceResponse<VerificationResponse>>;
}
