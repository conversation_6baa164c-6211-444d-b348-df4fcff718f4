'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'

interface ErrorBoundaryProps {
    /**
     * The component to render when an error occurs
     */
    fallback: ReactNode

    /**
     * Optional callback to be called when an error is caught
     */
    onError?: (error: Error, errorInfo: ErrorInfo) => void

    /**
     * Children to render
     */
    children: ReactNode
}

interface ErrorBoundaryState {
    /**
     * Whether an error has been caught
     */
    hasError: boolean

    /**
     * The error that was caught
     */
    error: Error | null
}

/**
 * Error boundary component to catch JavaScript errors in child components
 * and display a fallback UI instead of crashing the whole application
 *
 * @example
 * ```tsx
 * <ErrorBoundary fallback={<div>Something went wrong</div>}>
 *   <ComponentThatMightError />
 * </ErrorBoundary>
 * ```
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props)
        this.state = {
            hasError: false,
            error: null,
        }
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        // Update state so the next render will show the fallback UI
        return {
            hasError: true,
            error,
        }
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        // Log the error to an error reporting service
        console.error('Error caught by ErrorBoundary:', error, errorInfo)

        // Call the onError callback if provided
        if (this.props.onError) {
            this.props.onError(error, errorInfo)
        }
    }

    render(): ReactNode {
        if (this.state.hasError) {
            // Render the fallback UI
            return this.props.fallback
        }

        // Render children if no error
        return this.props.children
    }
}

export default ErrorBoundary
