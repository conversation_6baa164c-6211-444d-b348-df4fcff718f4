/**
 * Page data for the application
 * This file contains the base page data structure for the application
 * Some sections (like brand-list and service-categories) will be populated with dynamic data from external APIs
 * In a production environment, all of this would be stored in a database
 */

import { PageData } from '../types';

export const pageData: PageData = {
    sections: [
        {
            type: 'header',
            label: 'Header',
            visible: true, // Always visible
            value: {
                location: '400078',
                Image: 'assets/images/wify_logo.png',
                logoText: 'Wify',
                navLinks: [
                    { href: '/shop', label: 'Shop' },
                    { href: '/cart', label: 'Cart' },
                    { href: '/profile', label: 'Account' },
                ],
                businessLink: 'For Business',
            },
        },
        {
            type: 'search-service',
            label: 'Search Services',
            visible: true, // Can be toggled
            value: {
                location: '400092',
                title: 'WIFY',
                subheading: 'Book Trained Technicians for home Services',
                searchPlaceholder: 'Search here',
                backgroundImage:
                    'https://via.placeholder.com/1200x400?text=Hero+Banner',
                buttonText: 'Search',
            },
        },
        {
            type: 'banner',
            label: 'Services',
            visible: true, // Can be toggled
            value: {
                banners: [
                    {
                        label: 'Home Repairs By Certified Technicians',
                        subheading: 'Carpenters, Electricians, Plumbers',
                        backgroundImage:
                            'https://images.unsplash.com/photo-1581578731548-c64695cc6952?auto=format&fit=crop&q=80&w=400&h=200',
                    },
                    {
                        label: 'Professional Installation Services',
                        subheading: 'Expert technicians at your service',
                        backgroundImage:
                            'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?auto=format&fit=crop&q=80&w=400&h=200',
                    },
                    {
                        label: 'Quality Home Services',
                        subheading: 'Reliable and efficient solutions',
                        backgroundImage:
                            'https://images.unsplash.com/photo-1517646287270-a5a9ca602e5c?auto=format&fit=crop&q=80&w=400&h=200',
                    },
                ],
            },
        },
        {
            type: 'service-categories',
            label: 'Book A Professional Technician For-',
            visible: true, // Can be toggled
            value: {
                categories: [], // Empty array that will be populated with dynamic data from the service categories service
            },
        },
        {
            type: 'brand-list',
            label: 'Book Your Home Services By Brands',
            visible: true, // Can be toggled
            value: {
                brands: [], // Empty array that will be populated with dynamic data from the brands service
            },
        },
        {
            type: 'b2b-registration',
            label: 'Wify For Business',
            visible: true, // Can be toggled
            value: {
                subheading: 'Need installation, warranty, services?',
                buttonText: 'Register Now',
                buttonLink: '/register',
                features: [
                    {
                        label: 'Priority Support',
                        icon: 'check-circle',
                    },
                    {
                        label: 'Bulk Discounts',
                        icon: 'check-circle',
                    },
                    {
                        label: 'GST Invoicing',
                        icon: 'check-circle',
                    },
                    {
                        label: 'Account Manager',
                        icon: 'check-circle',
                    },
                ],
            },
        },
        {
            type: 'stats',
            label: 'Our Impact',
            visible: true, // Can be toggled
            iconImage: 'https://static.wify.co.in/images/uecom/Sparkle.svg',
            value: {
                stats: [
                    {
                        label: 'Furniture Assemblies',
                        value: '2.4M+',
                    },
                    {
                        label: 'Smart Homes Installation',
                        value: '50,000+',
                    },
                    {
                        label: 'Modular Installations',
                        value: '20,000+',
                    },
                    {
                        label: 'Happy Customers',
                        value: '1M+',
                    },
                ],
            },
        },
        {
            type: 'review-list',
            label: 'Reviews',
            visible: true, // Can be toggled
            value: {
                reviews: [
                    {
                        text: 'Quick and professional! The team provided accurate measurements and helpful insights for my kitchen renovation.',
                        author: 'Jainish Zobalia',
                        rating: 3,
                        date: '2 days ago',
                    },
                    {
                        text: "The electrician understood it was a rented apartment and therefore didn't put much effort into offering proper solutions.",
                        author: 'Saumya Gosh',
                        rating: 1,
                        date: '3 days ago',
                    },
                    {
                        text: 'Excellent service! The plumber was on time and fixed the issue efficiently.',
                        author: 'Nikhil Jagdale',
                        rating: 5,
                        date: '1 week ago',
                    },
                ],
            },
        },
        {
            type: 'footer',
            label: 'Footer',
            visible: true, // Always visible
            value: {
                tagline: 'We Innovate For You',
                address:
                    'D-704, Remi Bizcourt, Off Veera Desai Road, Andheri, West Mumbai 400053',
                phone: '+91-93214 20200',
                email: '<EMAIL>',
                legalTitle: 'Legal',
                companyTitle: 'Company',
                connectTitle: 'Connect With Us',
                copyright: 'Copyright',
                companyName: 'WIFY Technologies Private Limited',
                legalLinks: [
                    {
                        label: 'Privacy Policy',
                        url: 'https://home.wify.co.in/privacy-policy',
                    },
                    {
                        label: 'Terms & Conditions',
                        url: 'https://home.wify.co.in/terms-and-conditions',
                    },
                    {
                        label: 'Refund Policy',
                        url: 'https://home.wify.co.in/refund-policy',
                    },
                    {
                        label: 'Cancellation Policy',
                        url: 'https://home.wify.co.in/cancellation-policy',
                    },
                ],
                companyLinks: [
                    {
                        label: 'About Us',
                        url: 'https://home.wify.co.in/about-us',
                    },
                    {
                        label: 'Wify for Business',
                        url: 'https://home.wify.co.in/wify-for-business',
                    },
                    {
                        label: 'Work with us',
                        url: 'https://home.wify.co.in/onboarding',
                    },
                    { label: 'Career', url: 'https://home.wify.co.in/career' },
                    {
                        label: 'Knowledge center',
                        url: 'https://home.wify.co.in/learn',
                    },
                    { label: 'Blogs', url: 'https://home.wify.co.in/blogs' },
                ],
                socials: [
                    {
                        label: 'Instagram',
                        url: 'https://www.instagram.com/wify_world/',
                        icon: 'instagram',
                    },
                    {
                        label: 'LinkedIn',
                        url: 'https://www.linkedin.com/company/wifytechnologies/mycompany/',
                        icon: 'linkedin',
                    },
                    {
                        label: 'Facebook',
                        url: 'https://www.facebook.com/wify4you/',
                        icon: 'facebook',
                    },
                ],
            },
        },
        {
            type: 'navigation',
            label: 'Mobile Bottom Navigation',
            visible: true, // Always visible
            value: {
                tabs: [
                    {
                        id: 'home',
                        icon: 'Home',
                        label: 'Home',
                        path: '/discovery',
                    },
                    {
                        id: 'shop',
                        icon: 'ShoppingBag',
                        label: 'Shop',
                        path: '/shop',
                    },
                    {
                        id: 'track',
                        icon: 'Map',
                        label: 'Track',
                        path: '/track',
                    },
                    {
                        id: 'profile',
                        icon: 'User',
                        label: 'Account',
                        path: '/profile',
                    },
                ],
            },
        },
    ],
};
