import axios, { AxiosResponse, AxiosRequestConfig, AxiosInstance, AxiosError } from 'axios'
import { handleError } from './response-utils'

// Create a singleton axios instance with interceptors
let axiosInstance: AxiosInstance | null = null
let isRefreshing = false
let refreshSubscribers: Array<(token: string) => void> = []

// Token refresh function will be set later to avoid circular dependencies
let refreshTokenFunction: ((refreshToken: string) => Promise<any>) | null = null

/**
 * Set the token refresh function
 * This is called from the auth module to avoid circular dependencies
 *
 * @param refreshFn - Function to refresh the token
 */
export const setRefreshTokenFunction = (refreshFn: (refreshToken: string) => Promise<any>) => {
    refreshTokenFunction = refreshFn
}

/**
 * Get the axios instance with interceptors
 * Creates a new instance if one doesn't exist
 *
 * @returns Axios instance with interceptors
 */
export const getAxiosInstance = (): AxiosInstance => {
    if (!axiosInstance) {
        axiosInstance = axios.create({
            baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            withCredentials: true, // Important for cookies
        })

        // Set up interceptors
        setupInterceptors(axiosInstance)
    }
    return axiosInstance
}

/**
 * Set up request and response interceptors for the axios instance
 *
 * @param instance - Axios instance
 */
const setupInterceptors = (instance: AxiosInstance) => {
    // Request interceptor
    instance.interceptors.request.use(
        (config) => {
            // Don't add auth header for auth-related endpoints
            if (config.url?.includes('/auth/') && !config.url?.includes('/refresh-token')) {
                return config
            }

            // Add auth token from localStorage if available
            const token = getAccessToken()
            if (token && config.headers) {
                // Use set method if headers is an AxiosHeaders instance
                if (typeof config.headers.set === 'function') {
                    config.headers.set('Authorization', `Bearer ${token}`)
                } else {
                    // Fallback for older Axios versions
                    config.headers['Authorization'] = `Bearer ${token}`
                }
            }
            return config
        },
        (error) => Promise.reject(error),
    )

    // Response interceptor
    instance.interceptors.response.use(
        (response) => response,
        async (error: AxiosError) => {
            const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean }

            // If it's not an auth error or we've already tried to refresh, reject
            if (!error.response || error.response.status !== 401 || originalRequest._retry) {
                return Promise.reject(error)
            }

            // Check if the error is due to token expiration
            const errorCode = (error.response.data as any)?.error?.code
            if (errorCode !== 'TOKEN_EXPIRED' && errorCode !== 'INVALID_TOKEN') {
                return Promise.reject(error)
            }

            // Set retry flag
            originalRequest._retry = true

            // If we're already refreshing, queue this request
            if (isRefreshing) {
                return new Promise((resolve) => {
                    addRefreshSubscriber((token: string) => {
                        if (originalRequest.headers) {
                            // Use set method if headers is an AxiosHeaders instance
                            if (typeof originalRequest.headers.set === 'function') {
                                originalRequest.headers.set('Authorization', `Bearer ${token}`)
                            } else {
                                // Fallback for older Axios versions
                                originalRequest.headers['Authorization'] = `Bearer ${token}`
                            }
                        }
                        resolve(instance(originalRequest))
                    })
                })
            }

            // Start refreshing
            isRefreshing = true

            try {
                // Get refresh token
                const refreshToken = getRefreshToken()
                if (!refreshToken || !refreshTokenFunction) {
                    handleAuthFailure()
                    return Promise.reject(error)
                }

                // Call refresh token API
                const response = await refreshTokenFunction(refreshToken)
                const newAccessToken = response.data?.accessToken

                if (!newAccessToken) {
                    handleAuthFailure()
                    return Promise.reject(error)
                }

                // Store the new token
                storeAccessToken(newAccessToken)

                // Update authorization header
                if (originalRequest.headers) {
                    // Use set method if headers is an AxiosHeaders instance
                    if (typeof originalRequest.headers.set === 'function') {
                        originalRequest.headers.set('Authorization', `Bearer ${newAccessToken}`)
                    } else {
                        // Fallback for older Axios versions
                        originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`
                    }
                }

                // Execute all queued requests with new token
                onRefreshSuccess(newAccessToken)

                // Retry the original request
                return instance(originalRequest)
            } catch (refreshError) {
                // If refresh fails, clear auth and redirect to login
                handleAuthFailure()
                return Promise.reject(refreshError)
            } finally {
                isRefreshing = false
            }
        },
    )
}

/**
 * Add a subscriber to be called when token refresh is successful
 *
 * @param callback - Function to call with the new token
 */
const addRefreshSubscriber = (callback: (token: string) => void): void => {
    refreshSubscribers.push(callback)
}

/**
 * Execute all subscribers with the new token
 *
 * @param token - New access token
 */
const onRefreshSuccess = (token: string): void => {
    refreshSubscribers.forEach((callback) => callback(token))
    refreshSubscribers = []
}

/**
 * Handle authentication failure
 * Clears auth data and redirects to login
 */
const handleAuthFailure = (): void => {
    // Clear auth data
    localStorage.removeItem('auth-storage')

    // Redirect to login page if in browser context
    if (typeof window !== 'undefined') {
        window.location.href = '/auth'
    }
}

/**
 * Get the access token from localStorage
 *
 * @returns Access token or null if not found
 */
const getAccessToken = (): string | null => {
    if (typeof window === 'undefined') return null

    try {
        const authData = localStorage.getItem('auth-storage')
        if (!authData) return null

        const parsedData = JSON.parse(authData)
        return parsedData.state?.tokens?.accessToken || null
    } catch (error) {
        console.error('Error getting access token:', error)
        return null
    }
}

/**
 * Get the refresh token from localStorage
 *
 * @returns Refresh token or null if not found
 */
const getRefreshToken = (): string | null => {
    if (typeof window === 'undefined') return null

    try {
        const authData = localStorage.getItem('auth-storage')
        if (!authData) return null

        const parsedData = JSON.parse(authData)
        return parsedData.state?.tokens?.refreshToken || null
    } catch (error) {
        console.error('Error getting refresh token:', error)
        return null
    }
}

/**
 * Store the access token in localStorage
 *
 * @param token - Access token to store
 */
const storeAccessToken = (token: string): void => {
    if (typeof window === 'undefined') return

    try {
        const authData = localStorage.getItem('auth-storage')
        if (!authData) return

        const parsedData = JSON.parse(authData)
        if (parsedData.state?.tokens) {
            parsedData.state.tokens.accessToken = token
            localStorage.setItem('auth-storage', JSON.stringify(parsedData))
        }
    } catch (error) {
        console.error('Error storing access token:', error)
    }
}

// Export convenience methods
export const api = {
    get: <T>(url: string, config?: AxiosRequestConfig) =>
        getAxiosInstance()
            .get<T>(url, config)
            .then((response) => response.data),
    post: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
        getAxiosInstance()
            .post<T>(url, data, config)
            .then((response) => response.data),
    put: <T>(url: string, data?: any, config?: AxiosRequestConfig) =>
        getAxiosInstance()
            .put<T>(url, data, config)
            .then((response) => response.data),
    delete: <T>(url: string, config?: AxiosRequestConfig) =>
        getAxiosInstance()
            .delete<T>(url, config)
            .then((response) => response.data),
}

const handleSuccess = <T>(response: AxiosResponse<T>): T => {
    return response.data
}

/**
 * Make a GET request with authentication
 *
 * @param url - URL to request
 * @param config - Axios request config
 * @returns Promise with response data
 */
export const doGetCall = async <T>(url: string, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
        return await api.get<T>(url, config)
    } catch (error) {
        return handleError(error, url)
    }
}

/**
 * Make a POST request with authentication
 *
 * @param url - URL to request
 * @param data - Data to send
 * @param config - Axios request config
 * @returns Promise with response data
 */
export const doPostCall = async <T>(
    url: string,
    data: any,
    config: AxiosRequestConfig = {},
): Promise<T> => {
    try {
        return await api.post<T>(url, data, config)
    } catch (error) {
        return handleError(error, url)
    }
}

/**
 * Make a POST request without authentication
 * Used primarily for auth endpoints
 *
 * @param url - URL to request
 * @param data - Data to send
 * @param config - Axios request config
 * @returns Promise with response data
 */
export const doPlainPostCall = async <T>(
    url: string,
    data: any,
    config: AxiosRequestConfig = {},
): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axios.post(url, data, config)
        return handleSuccess(response)
    } catch (error) {
        return handleError(error, url)
    }
}

/**
 * Make a PUT request with authentication
 *
 * @param url - URL to request
 * @param data - Data to send
 * @param config - Axios request config
 * @returns Promise with response data
 */
export const doPutCall = async <T>(
    url: string,
    data: any,
    config: AxiosRequestConfig = {},
): Promise<T> => {
    try {
        return await api.put<T>(url, data, config)
    } catch (error) {
        return handleError(error, url)
    }
}

/**
 * Make a DELETE request with authentication
 *
 * @param url - URL to request
 * @param config - Axios request config
 * @returns Promise with response data
 */
export const doDeleteCall = async <T>(url: string, config: AxiosRequestConfig = {}): Promise<T> => {
    try {
        return await api.delete<T>(url, config)
    } catch (error) {
        return handleError(error, url)
    }
}
