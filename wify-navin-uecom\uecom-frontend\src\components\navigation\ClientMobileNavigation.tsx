'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Home, ShoppingBag, Map, User, LucideIcon } from 'lucide-react'
import { NavigationTab } from '../../types'
import { useDevice } from '../../hooks/useDevice'
import { DeviceType } from '../../lib/constants/breakpoints'

interface ClientMobileNavigationProps {
    tabs: NavigationTab[]
}

/**
 * Client-side mobile navigation component
 * Handles interactive elements and device detection
 */
export default function ClientMobileNavigation({ tabs }: ClientMobileNavigationProps) {
    const pathname = usePathname()
    const { isMounted, deviceType } = useDevice()

    // Map icon strings to actual icon components
    const iconMap: Record<string, LucideIcon> = {
        Home,
        ShoppingBag,
        Map,
        User,
    }

    // Only show on mobile devices and after mounting
    if (!isMounted || deviceType !== DeviceType.Mobile) {
        return null
    }

    return (
        <div
            className="fixed bottom-0 left-0 right-0 bg-white shadow-lg z-[9999] border-t border-gray-200"
            style={{
                boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
                display: 'block',
                visibility: 'visible' as const,
            }}
        >
            <div className="flex justify-around items-center h-[72px] px-4">
                {tabs.map((tab) => {
                    const isActive = pathname === tab.path
                    const TabIcon = iconMap[tab.icon] || Home // Fallback to Home icon if not found

                    return (
                        <Link
                            key={tab.id}
                            href={tab.path}
                            className="flex flex-col items-center flex-1"
                        >
                            <button className="flex flex-col items-center justify-center w-full">
                                {isActive ? (
                                    <div className="flex items-center gap-2 px-4 py-2 bg-orange-100 rounded-full">
                                        <TabIcon className="h-5 w-5 text-orange-500" />
                                        <span className="text-[13px] text-orange-500 font-medium">
                                            {tab.label}
                                        </span>
                                    </div>
                                ) : (
                                    <div className="flex flex-col items-center">
                                        <TabIcon className="h-5 w-5 text-gray-500" />
                                        {/* <span className="text-[11px] mt-1 text-gray-500">{tab.label}</span> */}
                                    </div>
                                )}
                            </button>
                        </Link>
                    )
                })}
            </div>
        </div>
    )
}
