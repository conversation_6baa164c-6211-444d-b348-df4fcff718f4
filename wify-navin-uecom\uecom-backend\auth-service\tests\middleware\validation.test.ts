import request from 'supertest';
import express from 'express';
import {
    validateRequestOtp,
    validateVerifyOtp,
} from '../../src/middlewares/validation';

describe('Validation Middleware', () => {
    let app: express.Express;

    beforeEach(() => {
        app = express();
        app.use(express.json());

        app.post('/api/v1/auth/request-otp', validateRequestOtp, (req, res) => {
            res.status(200).json({ message: 'OTP request received' });
        });

        app.post('/api/v1/auth/verify-otp', validateVerifyOtp, (req, res) => {
            res.status(200).json({ message: 'OTP verified successfully' });
        });
    });

    describe('validateRequestOtp', () => {
        it('should return a 400 status if mobile is missing', async () => {
            const response = await request(app)
                .post('/api/v1/auth/request-otp')
                .send({});

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile number' });
        });

        it('should return a 400 status if mobile is not a string', async () => {
            const response = await request(app)
                .post('/api/v1/auth/request-otp')
                .send({ mobile: 1234567890 });

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile number' });
        });

        it('should return a 400 status if mobile is invalid (not a 10-digit number)', async () => {
            const response = await request(app)
                .post('/api/v1/auth/request-otp')
                .send({ mobile: '12345' });

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile number' });
        });

        it('should pass validation if mobile is a valid 10-digit string', async () => {
            const response = await request(app)
                .post('/api/v1/auth/request-otp')
                .send({ mobile: '1234567890' });
            expect(response.status).toBe(200);
            expect(response.body).toEqual({ message: 'OTP request received' });
        });
    });

    describe('validateVerifyOtp', () => {
        it('should return a 400 status if mobile is missing', async () => {
            const response = await request(app)
                .post('/api/v1/auth/verify-otp')
                .send({ otp: '123456' });

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile or OTP' });
        });

        it('should return a 400 status if otp is missing', async () => {
            const response = await request(app)
                .post('/api/v1/auth/verify-otp')
                .send({ mobile: '1234567890' });

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile or OTP' });
        });

        it('should return a 400 status if mobile is invalid (not a 10-digit string)', async () => {
            const response = await request(app)
                .post('/api/v1/auth/verify-otp')
                .send({ mobile: '12345', otp: '123456' });

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile or OTP' });
        });

        it('should return a 400 status if otp is invalid (not a 6-digit string)', async () => {
            const response = await request(app)
                .post('/api/v1/auth/verify-otp')
                .send({ mobile: '1234567890', otp: '12345' });

            expect(response.status).toBe(400);
            expect(response.body).toEqual({ error: 'Invalid mobile or OTP' });
        });

        it('should pass validation if mobile is valid and otp is valid 6-digit string', async () => {
            const response = await request(app)
                .post('/api/v1/auth/verify-otp')
                .send({ mobile: '1234567890', otp: '123456' });

            expect(response.status).toBe(200);
            expect(response.body).toEqual({
                message: 'OTP verified successfully',
            });
        });
    });
});
