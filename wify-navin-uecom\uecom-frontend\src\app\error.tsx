'use client'

import React, { useEffect } from 'react'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import Link from 'next/link'

interface ErrorPageProps {
    error: Error & { digest?: string }
    reset: () => void
}

/**
 * Error page component for handling client-side errors
 * This is a Next.js App Router error boundary
 */
export default function ErrorPage({ error, reset }: ErrorPageProps) {
    useEffect(() => {
        // Log the error to an error reporting service
        console.error('Application error:', error)
    }, [error])

    const handleReset = () => {
        // Clear any cached data if needed
        if (typeof window !== 'undefined') {
            // Clear localStorage if needed
            // localStorage.clear()
        }
        reset()
    }

    return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
            <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
                {/* Error Icon */}
                <div className="flex justify-center mb-6">
                    <div className="bg-red-100 rounded-full p-4">
                        <AlertTriangle className="h-12 w-12 text-red-600" />
                    </div>
                </div>

                {/* Error Title */}
                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    Something went wrong
                </h1>

                {/* Error Message */}
                <p className="text-gray-600 mb-6">
                    We're sorry, but there was an unexpected error. Please try refreshing the page or go back to the home page.
                </p>

                {/* Error Details (only in development) */}
                {process.env.NODE_ENV === 'development' && (
                    <div className="bg-gray-100 rounded-lg p-4 mb-6 text-left">
                        <h3 className="font-semibold text-gray-900 mb-2">Error Details:</h3>
                        <p className="text-sm text-gray-700 font-mono break-all">
                            {error.message}
                        </p>
                        {error.digest && (
                            <p className="text-xs text-gray-500 mt-2">
                                Error ID: {error.digest}
                            </p>
                        )}
                    </div>
                )}

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-3">
                    <button
                        onClick={handleReset}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                        <RefreshCw className="h-4 w-4" />
                        Try Again
                    </button>
                    
                    <Link
                        href="/"
                        className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                    >
                        <Home className="h-4 w-4" />
                        Go Home
                    </Link>
                </div>

                {/* Additional Help */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                    <p className="text-sm text-gray-500">
                        If this problem persists, please{' '}
                        <Link href="/help" className="text-blue-600 hover:text-blue-700 underline">
                            contact support
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    )
}
