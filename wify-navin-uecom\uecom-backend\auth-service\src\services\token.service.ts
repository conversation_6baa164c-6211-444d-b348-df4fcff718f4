/**
 * Token Service
 *
 * This service handles JWT token generation, verification, and management.
 * It provides methods for creating access tokens, refresh tokens, and verifying tokens.
 *
 * @module services/token
 */

import * as jwt from 'jsonwebtoken';
import { ConfigService } from '../configs/config.service';

// Token types
export enum TokenType {
    ACCESS = 'access',
    REFRESH = 'refresh',
    SESSION = 'session',
}

// Token payload interface
export interface TokenPayload {
    sub: string; // Subject (user identifier)
    type: TokenType; // Token type
    iat?: number; // Issued at
    exp?: number; // Expiration time
    jti?: string; // JWT ID (unique identifier for this token)
}

export class TokenService {
    private readonly configService: ConfigService;

    // Token expiration times
    private readonly ACCESS_TOKEN_EXPIRY = '1h'; // 1 hour
    private readonly REFRESH_TOKEN_EXPIRY = '7d'; // 7 days

    constructor() {
        this.configService = new ConfigService();
    }

    /**
     * Generates a JWT access token for a user
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT access token
     */
    generateAccessToken(userId: string): string {
        const secret = this.configService.getJwtSecret();

        const payload: TokenPayload = {
            sub: userId,
            type: TokenType.ACCESS,
            jti: this.generateTokenId(),
        };

        // Use a simple approach to avoid TypeScript issues
        return jwt.sign(payload, secret, {
            expiresIn: this.ACCESS_TOKEN_EXPIRY,
        });
    }

    /**
     * Generates a JWT refresh token for a user
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT refresh token
     */
    generateRefreshToken(userId: string): string {
        const secret = this.configService.getJwtSecret();

        const payload: TokenPayload = {
            sub: userId,
            type: TokenType.REFRESH,
            jti: this.generateTokenId(),
        };

        // Use a simple approach to avoid TypeScript issues
        return jwt.sign(payload, secret, {
            expiresIn: this.REFRESH_TOKEN_EXPIRY,
        });
    }

    /**
     * Generates a JWT session token for a user
     * This token is meant to be stored in a secure HTTP-only cookie
     *
     * @param {string} userId - The user's unique identifier
     * @returns {string} The generated JWT session token
     */
    generateSessionToken(userId: string): string {
        const secret = this.configService.getJwtSecret();
        const expiryHours = this.configService.getJwtSessionExpiryHours();
        const expiryTime = `${expiryHours}h`;

        const payload: TokenPayload = {
            sub: userId,
            type: TokenType.SESSION,
            jti: this.generateTokenId(),
        };

        // Use a simple approach to avoid TypeScript issues
        return jwt.sign(payload, secret, {
            expiresIn: expiryTime,
        });
    }

    /**
     * Verifies a JWT token and returns the decoded payload
     *
     * @param {string} token - The JWT token to verify
     * @param {TokenType} [expectedType] - The expected token type
     * @returns {TokenPayload} The decoded token payload
     * @throws {Error} If the token is invalid or expired
     */
    verifyToken(token: string, expectedType?: TokenType): TokenPayload {
        try {
            const secret = this.configService.getJwtSecret();
            // Ensure secret is a Buffer or string for jwt.verify
            const decoded = jwt.verify(token, String(secret)) as TokenPayload;

            // Verify token type if expected type is provided
            if (expectedType && decoded.type !== expectedType) {
                throw new Error(
                    `Invalid token type: expected ${expectedType}, got ${decoded.type}`
                );
            }

            return decoded;
        } catch (error: any) {
            if (error.name === 'TokenExpiredError') {
                throw new Error('Token has expired');
            } else if (error.name === 'JsonWebTokenError') {
                throw new Error('Invalid token');
            }
            throw error;
        }
    }

    /**
     * Generates a unique token ID
     *
     * @private
     * @returns {string} A unique token ID
     */
    private generateTokenId(): string {
        return (
            Date.now().toString(36) +
            Math.random().toString(36).substring(2, 15)
        );
    }
}

// Export a singleton instance
export const tokenService = new TokenService();
