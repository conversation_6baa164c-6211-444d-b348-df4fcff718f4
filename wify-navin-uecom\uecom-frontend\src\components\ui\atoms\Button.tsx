'use client'

import React, { useMemo } from 'react'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '../../../lib/utils/cn'

// Define button variants using class-variance-authority
const buttonVariants = cva(
    'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none',
    {
        variants: {
            variant: {
                default: 'bg-primary text-primary-foreground hover:bg-primary/90',
                destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
                outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
                secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
                ghost: 'hover:bg-accent hover:text-accent-foreground',
                link: 'underline-offset-4 hover:underline text-primary',
                // Add a new accessible variant with high contrast
                highContrast: 'bg-black text-white hover:bg-gray-800 border-2 border-white',
            },
            size: {
                default: 'h-10 py-2 px-4',
                sm: 'h-9 px-3 rounded-md',
                lg: 'h-11 px-8 rounded-md',
                icon: 'h-10 w-10',
                // Add a new size for touch targets on mobile
                touch: 'h-12 px-4 rounded-md', // Larger height for better touch targets
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    },
)

// Define button props interface with enhanced accessibility props
export interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean
    /**
     * Accessible label for screen readers when button text is not descriptive enough
     */
    accessibleLabel?: string
    /**
     * Whether the button is currently loading
     */
    isLoading?: boolean
    /**
     * Icon to display before the button text
     */
    leftIcon?: React.ReactNode
    /**
     * Icon to display after the button text
     */
    rightIcon?: React.ReactNode
}

/**
 * Button component with various styles and sizes
 * Optimized with memoization and enhanced with accessibility features
 *
 * @example
 * ```tsx
 * <Button variant="default" size="default">Click me</Button>
 * <Button variant="outline" size="sm" accessibleLabel="Edit profile settings">Edit</Button>
 * <Button variant="destructive" size="lg" isLoading={isSubmitting}>Delete</Button>
 * ```
 */
const Button = React.memo(
    React.forwardRef<HTMLButtonElement, ButtonProps>(
        (
            {
                className,
                variant,
                size,
                asChild = false,
                accessibleLabel,
                isLoading = false,
                leftIcon,
                rightIcon,
                children,
                disabled,
                ...props
            },
            ref,
        ) => {
            // Combine button variants with className
            const buttonClasses = useMemo(() => {
                return cn(buttonVariants({ variant, size, className }))
            }, [variant, size, className])

            // Determine if button should be disabled
            const isDisabled = useMemo(() => {
                return disabled || isLoading
            }, [disabled, isLoading])

            return (
                <button
                    className={buttonClasses}
                    ref={ref}
                    disabled={isDisabled}
                    aria-label={accessibleLabel}
                    aria-busy={isLoading}
                    {...props}
                >
                    {isLoading && (
                        <span className="mr-2 inline-block h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    )}
                    {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
                    {children}
                    {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
                </button>
            )
        },
    ),
)

Button.displayName = 'Button'

export { Button, buttonVariants }
