/**
 * Rate Limiter Middleware
 *
 * This middleware provides rate limiting functionality to protect against
 * brute force attacks and abuse. It uses express-rate-limit to implement
 * IP-based and route-specific rate limiting.
 *
 * @module middlewares/rate-limiter
 */

import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { createRedisStore } from '../services/redis-rate-limit-store';

// Default rate limit configuration
const DEFAULT_WINDOW_MS = 15 * 60 * 1000; // 15 minutes
const DEFAULT_MAX_REQUESTS = 100; // 100 requests per windowMs

// Function to create Redis stores with different prefixes
const createStores = () => {
    try {
        return {
            apiRedisStore: createRedisStore('api:'),
            authRedisStore: createRedisStore('auth:'),
            otpVerificationRedisStore: createRedisStore('otpv:'),
            otpRequestRedisStore: createRedisStore('otpr:'),
        };
    } catch (error) {
        console.error('❌ Error creating Redis stores:', error);
        // Return undefined for all stores to fall back to memory store
        return {
            apiRedisStore: undefined,
            authRedisStore: undefined,
            otpVerificationRedisStore: undefined,
            otpRequestRedisStore: undefined,
        };
    }
};

// Function to create rate limiters with appropriate stores
export const createRateLimiters = () => {
    // Get stores
    const {
        apiRedisStore,
        authRedisStore,
        otpVerificationRedisStore,
        otpRequestRedisStore,
    } = createStores();

    // Create a general API rate limiter
    const apiLimiter = rateLimit({
        windowMs: DEFAULT_WINDOW_MS,
        max: DEFAULT_MAX_REQUESTS,
        standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
        legacyHeaders: false, // Disable the `X-RateLimit-*` headers
        store: apiRedisStore, // Use Redis store for distributed rate limiting
        message: {
            statusCode: 429,
            statusMessage: 'Too Many Requests',
            message: 'Too many requests, please try again later.',
            error: {
                code: 'RATE_LIMIT_EXCEEDED',
            },
        },
        // Custom handler to maintain consistent API response format
        handler: (req: Request, res: Response) => {
            res.status(429).json({
                statusCode: 429,
                statusMessage: 'Too Many Requests',
                message: 'Too many requests, please try again later.',
                error: {
                    code: 'RATE_LIMIT_EXCEEDED',
                },
            });
        },
    });

    // Create a stricter rate limiter for authentication endpoints
    const authLimiter = rateLimit({
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 10, // 10 requests per hour
        standardHeaders: true,
        legacyHeaders: false,
        store: authRedisStore, // Use Redis store for distributed rate limiting
        message: {
            statusCode: 429,
            statusMessage: 'Too Many Requests',
            message:
                'Too many authentication attempts, please try again later.',
            error: {
                code: 'AUTH_RATE_LIMIT_EXCEEDED',
            },
        },
        // Custom handler to maintain consistent API response format
        handler: (req: Request, res: Response) => {
            res.status(429).json({
                statusCode: 429,
                statusMessage: 'Too Many Requests',
                message:
                    'Too many authentication attempts, please try again later.',
                error: {
                    code: 'AUTH_RATE_LIMIT_EXCEEDED',
                },
            });
        },
        // Skip if in development mode
        skip: (req: Request) => process.env.NODE_ENV === 'development',
    });

    // Create a very strict rate limiter for OTP verification to prevent brute force
    const otpVerificationLimiter = rateLimit({
        windowMs: 60 * 60 * 1000, // 1 hour
        max: 5, // 5 attempts per hour
        standardHeaders: true,
        legacyHeaders: false,
        store: otpVerificationRedisStore, // Use Redis store for distributed rate limiting
        message: {
            statusCode: 429,
            statusMessage: 'Too Many Requests',
            message:
                'Too many OTP verification attempts, please try again later.',
            error: {
                code: 'OTP_VERIFICATION_RATE_LIMIT_EXCEEDED',
            },
        },
        // Custom handler to maintain consistent API response format
        handler: (req: Request, res: Response) => {
            res.status(429).json({
                statusCode: 429,
                statusMessage: 'Too Many Requests',
                message:
                    'Too many OTP verification attempts, please try again later.',
                error: {
                    code: 'OTP_VERIFICATION_RATE_LIMIT_EXCEEDED',
                },
            });
        },
        // Skip if in development mode
        skip: (req: Request) => process.env.NODE_ENV === 'development',
    });

    // Create a moderate rate limiter for OTP requests
    const otpRequestLimiter = rateLimit({
        windowMs: 15 * 60 * 1000, // 15 minutes
        max: 3, // 3 OTP requests per 15 minutes
        standardHeaders: true,
        legacyHeaders: false,
        store: otpRequestRedisStore, // Use Redis store for distributed rate limiting
        message: {
            statusCode: 429,
            statusMessage: 'Too Many Requests',
            message: 'Too many OTP requests, please try again later.',
            error: {
                code: 'OTP_REQUEST_RATE_LIMIT_EXCEEDED',
            },
        },
        // Custom handler to maintain consistent API response format
        handler: (req: Request, res: Response) => {
            res.status(429).json({
                statusCode: 429,
                statusMessage: 'Too Many Requests',
                message: 'Too many OTP requests, please try again later.',
                error: {
                    code: 'OTP_REQUEST_RATE_LIMIT_EXCEEDED',
                },
            });
        },
        // Skip if in development mode
        skip: (req: Request) => process.env.NODE_ENV === 'development',
    });

    return {
        apiLimiter,
        authLimiter,
        otpVerificationLimiter,
        otpRequestLimiter,
    };
};

// Export rate limiters (these will be initialized when imported)
export let apiLimiter: ReturnType<typeof rateLimit>;
export let authLimiter: ReturnType<typeof rateLimit>;
export let otpVerificationLimiter: ReturnType<typeof rateLimit>;
export let otpRequestLimiter: ReturnType<typeof rateLimit>;

// Initialize rate limiters (this will be called after DI container is set up)
export const initRateLimiters = () => {
    const limiters = createRateLimiters();
    apiLimiter = limiters.apiLimiter;
    authLimiter = limiters.authLimiter;
    otpVerificationLimiter = limiters.otpVerificationLimiter;
    otpRequestLimiter = limiters.otpRequestLimiter;

    console.log('✅ Rate limiters initialized with Redis stores');
};
