'use client'

import { useRef, useMemo, useCallback, useEffect, useState } from 'react'

/**
 * Options for the useMemoizedValue hook
 */
interface MemoizationOptions<T> {
    /**
     * Maximum number of items to cache
     * @default 100
     */
    maxCacheSize?: number

    /**
     * Time in milliseconds after which cached items expire
     * @default undefined (no expiration)
     */
    expirationTime?: number

    /**
     * Custom equality function to determine if values are equal
     * @default Object.is
     */
    isEqual?: (a: T, b: T) => boolean
}

/**
 * Hook for memoizing expensive calculations with advanced caching capabilities
 *
 * @param factory - Function that produces the value
 * @param dependencies - Array of dependencies that the factory depends on
 * @param options - Caching and memoization options
 * @returns The memoized value
 *
 * @example
 * ```tsx
 * // Basic usage
 * const expensiveValue = useMemoizedValue(() => computeExpensiveValue(a, b), [a, b]);
 *
 * // With options
 * const cachedValue = useMemoizedValue(
 *   () => fetchData(id),
 *   [id],
 *   { maxCacheSize: 50, expirationTime: 60000 }
 * );
 * ```
 */
export function useMemoizedValue<T>(
    factory: () => T,
    dependencies: React.DependencyList,
    options: MemoizationOptions<T> = {},
): T {
    // Default options
    const { maxCacheSize = 100, expirationTime, isEqual = Object.is } = options

    // Cache structure: Map of dependency arrays to { value, timestamp }
    type CacheEntry = { value: T; timestamp: number }
    const cacheRef = useRef<Map<string, CacheEntry>>(new Map())

    // Clean up expired cache entries
    useEffect(() => {
        if (!expirationTime) return

        const interval = setInterval(
            () => {
                const now = Date.now()
                const cache = cacheRef.current

                // Remove expired entries
                for (const [key, entry] of cache.entries()) {
                    if (now - entry.timestamp > expirationTime) {
                        cache.delete(key)
                    }
                }
            },
            Math.min(expirationTime, 60000),
        ) // Clean up at most once per minute

        return () => clearInterval(interval)
    }, [expirationTime])

    // Generate a stable key from dependencies
    const depsKey = useMemo(() => {
        try {
            return JSON.stringify(dependencies)
        } catch (e) {
            // If dependencies can't be stringified, fall back to joining them
            return dependencies.map((d) => String(d)).join('|')
        }
    }, dependencies)

    // Check if we have a valid cached value
    const cachedEntry = cacheRef.current.get(depsKey)
    const now = Date.now()
    const isCacheValid =
        cachedEntry && (!expirationTime || now - cachedEntry.timestamp <= expirationTime)

    // Compute or retrieve the value
    const value = useMemo(() => {
        // Use cached value if valid
        if (isCacheValid) return cachedEntry!.value

        // Otherwise compute new value
        const newValue = factory()

        // Store in cache
        cacheRef.current.set(depsKey, { value: newValue, timestamp: Date.now() })

        // Enforce max cache size
        if (cacheRef.current.size > maxCacheSize) {
            const firstKey = cacheRef.current.keys().next().value
            if (firstKey !== undefined) {
                cacheRef.current.delete(firstKey)
            }
        }

        return newValue
    }, [depsKey, factory, isCacheValid])

    // Provide a way to clear the cache
    const clearCache = useCallback(() => {
        cacheRef.current.clear()
    }, [])

    return value
}

/**
 * Hook for memoizing a function with result caching
 *
 * @param fn - Function to memoize
 * @param dependencies - Array of dependencies that the function depends on
 * @param options - Caching and memoization options
 * @returns Memoized function that caches results based on arguments
 *
 * @example
 * ```tsx
 * // Memoize a function that fetches user data
 * const fetchUserMemoized = useMemoizedFunction(
 *   (userId: string) => fetchUserData(userId),
 *   [],
 *   { maxCacheSize: 20, expirationTime: 300000 } // 5 minutes cache
 * );
 * ```
 */
export function useMemoizedFunction<T extends (...args: any[]) => any>(
    fn: T,
    dependencies: React.DependencyList,
    options: MemoizationOptions<ReturnType<T>> = {},
): T {
    // Create a stable reference to the function
    const fnRef = useRef(fn)

    // Update the function reference when dependencies change
    useEffect(() => {
        fnRef.current = fn
    }, [fn, ...dependencies])

    // Cache structure: Map of stringified arguments to { result, timestamp }
    type CacheEntry = { result: ReturnType<T>; timestamp: number }
    const cacheRef = useRef<Map<string, CacheEntry>>(new Map())

    // Create the memoized function
    const memoizedFn = useCallback(
        (...args: Parameters<T>): ReturnType<T> => {
            const { maxCacheSize = 100, expirationTime, isEqual = Object.is } = options

            // Generate a key from the arguments
            const argsKey = JSON.stringify(args)

            // Check if we have a valid cached result
            const cachedEntry = cacheRef.current.get(argsKey)
            const now = Date.now()
            const isCacheValid =
                cachedEntry && (!expirationTime || now - cachedEntry.timestamp <= expirationTime)

            if (isCacheValid) {
                return cachedEntry!.result
            }

            // Call the function with the provided arguments
            const result = fnRef.current(...args)

            // Store the result in cache
            cacheRef.current.set(argsKey, { result, timestamp: now })

            // Enforce max cache size
            // Enforce max cache size
            if (cacheRef.current.size > maxCacheSize) {
                const firstKey = cacheRef.current.keys().next().value
                if (firstKey !== undefined) {
                    cacheRef.current.delete(firstKey)
                }
            }

            return result
        },
        [options],
    )

    // Cast to the original function type
    return memoizedFn as T
}
