import { SectionData } from '../types'
import { lazy, ComponentType } from 'react'

// Define component props type
export interface SectionComponentProps<T extends SectionData = SectionData> {
    data: T | null
}

// Define section component type
export type SectionComponent<T extends SectionData = SectionData> = ComponentType<
    SectionComponentProps<T>
>

// Define section registry type
export type SectionRegistry = Record<string, SectionComponent>

// Define section factory interface
export interface ISectionFactory {
    registerComponent(type: string, component: ComponentType<any>): void
    getComponent(type: string): SectionComponent | null
    hasComponent(type: string): boolean
}

/**
 * Section Factory implementation
 * Follows the Open/Closed Principle by allowing extension without modification
 * Follows the Single Responsibility Principle by focusing only on component registration and retrieval
 */
class SectionFactory implements ISectionFactory {
    private components: SectionRegistry = {}

    /**
     * Register a component for a specific section type
     * This method accepts any ComponentType and adapts it to the SectionComponent interface
     *
     * @param type - Section type
     * @param component - Component to render for this section type
     */
    registerComponent(type: string, component: ComponentType<any>): void {
        this.components[type] = component as SectionComponent
    }

    /**
     * Get component for a specific section type
     *
     * @param type - Section type
     * @returns Component for the section type or null if not found
     */
    getComponent(type: string): SectionComponent | null {
        return this.components[type] || null
    }

    /**
     * Check if a component exists for a specific section type
     *
     * @param type - Section type
     * @returns True if component exists, false otherwise
     */
    hasComponent(type: string): boolean {
        return !!this.components[type]
    }
}

// Create singleton instance
export const sectionFactory = new SectionFactory()

// Register default components
// Using lazy loading for better code splitting
sectionFactory.registerComponent(
    'header',
    lazy(() => import('../components/sections/Header')),
)
sectionFactory.registerComponent(
    'service-categories',
    lazy(() => import('../components/sections/ServiceCategories')),
)
sectionFactory.registerComponent(
    'brand-list',
    lazy(() => import('../components/sections/BrandList')),
)
sectionFactory.registerComponent(
    'review-list',
    lazy(() => import('../components/sections/ReviewList')),
)
sectionFactory.registerComponent(
    'banner',
    lazy(() => import('../components/sections/Banner')),
)
sectionFactory.registerComponent(
    'footer',
    lazy(() => import('../components/sections/Footer')),
)
sectionFactory.registerComponent(
    'stats',
    lazy(() => import('../components/sections/Stats')),
)
sectionFactory.registerComponent(
    'b2b-registration',
    lazy(() => import('../components/sections/b2BRegistration')),
)

// Register feature-based components
// Make sure the search-service component is registered with high priority
sectionFactory.registerComponent(
    'search-service',
    lazy(() => import('../components/sections/SearchService')),
)

// Register navigation component
sectionFactory.registerComponent(
    'navigation',
    lazy(() => import('../components/navigation/mobileBottomNavigation')),
)
