# Server Configuration
PORT=4000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_SESSION_EXPIRY_HOURS=24

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://your-production-domain.com

# SMS Gateway Configuration
MTALKZ_API_KEY=your_mtalkz_api_key_here
MTALKZ_SENDER_ID=INWIFY
MSG_BASE_URL=https://api.mtalkz.com
MSG_SUB_PATH=/V2/http-api.php

# Redis Configuration (REQUIRED)
# Redis is used for OTP storage and is required for the application to function properly
REDIS_URL=redis://localhost:6379
REDIS_OTP_PREFIX=otp:
OTP_EXPIRATION_SECONDS=900

# For production, use a secure Redis instance with authentication:
# REDIS_URL=redis://:password@your-redis-host:6379

# Cookie Configuration
# These are optional and have sensible defaults
# COOKIE_DOMAIN=your-domain.com
# COOKIE_PATH=/
# COOKIE_SECURE=true
# COOKIE_HTTP_ONLY=true
# COOKIE_SAME_SITE=strict

# Database Configuration (REQUIRED)
# This should be the same database URL as used by app-service
DATABASE_URL=postgresql://username:password@localhost:5432/wify_db
