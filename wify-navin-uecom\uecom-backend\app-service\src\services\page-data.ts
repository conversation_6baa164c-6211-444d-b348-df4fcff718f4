/**
 * Page Data Service
 *
 * This service provides methods for retrieving page data.
 * It combines static data with dynamic data from various sources.
 */

import {
    PageData,
    SectionData,
    BrandListSection,
    ServiceCategoriesSection,
} from '../types';
import { pageData } from '../data/pageData';
import { logger } from '../utils/logger';
import { brandsService } from './brands.service';
import { serviceCategoriesService } from './serviceCategories.service';

/**
 * Get page data by page name
 *
 * @param pageName - Name of the page to fetch data for
 * @returns Page data for the specified page
 */
export const getPageData = async (pageName: string): Promise<PageData> => {
    try {
        logger.info(`Fetching page data for: ${pageName}`);

        // Start with the static page data
        const data = JSON.parse(JSON.stringify(pageData)) as PageData;

        // Enhance with dynamic data
        await enhanceWithDynamicData(data);

        return data;
    } catch (error) {
        logger.error(`Error fetching page data for ${pageName}:`, error);
        // Return empty page data as fallback
        return { sections: [] };
    }
};

/**
 * Enhance page data with dynamic data from various sources
 *
 * @param pageData - Page data to enhance
 */
const enhanceWithDynamicData = async (pageData: PageData): Promise<void> => {
    try {
        // Enhance with dynamic brands data
        await enhanceBrandsData(pageData);

        // Enhance with dynamic service categories data
        await enhanceServiceCategoriesData(pageData);
    } catch (error) {
        logger.error('Error enhancing page data with dynamic data:', error);
        // Continue with static data if enhancement fails
    }
};

/**
 * Enhance page data with dynamic brands data
 *
 * @param pageData - Page data to enhance
 */
const enhanceBrandsData = async (pageData: PageData): Promise<void> => {
    try {
        // Find the brand list section
        const brandListSection = pageData.sections.find(
            (section) => section.type === 'brand-list'
        ) as BrandListSection | undefined;

        if (brandListSection) {
            // Fetch brands from the brands service
            const brandsData = await brandsService.getAllBrands();

            // Update the brand list section with dynamic data
            brandListSection.value.brands = brandsData.brands.map((brand) => ({
                label: brand.label,
                logoImage: brand.logoImage,
            }));

            logger.info('Enhanced page data with dynamic brands data');
        }
    } catch (error) {
        logger.error('Error enhancing brands data:', error);
        // Continue with static data if enhancement fails
    }
};

/**
 * Enhance page data with dynamic service categories data
 *
 * @param pageData - Page data to enhance
 */
const enhanceServiceCategoriesData = async (
    pageData: PageData
): Promise<void> => {
    try {
        // Find the service categories section
        const serviceCategoriesSection = pageData.sections.find(
            (section) => section.type === 'service-categories'
        ) as ServiceCategoriesSection | undefined;

        if (serviceCategoriesSection) {
            // Fetch service categories from the service
            const serviceCategoriesData =
                await serviceCategoriesService.getAllServiceCategories();

            // Update the service categories section with dynamic data
            serviceCategoriesSection.value.categories =
                serviceCategoriesData.categories.map((category) => ({
                    label: category.label,
                    description: category.description,
                    image: category.image,
                    iconName: category.iconName,
                }));

            logger.info(
                `Enhanced page data with ${serviceCategoriesData.categories.length} dynamic service categories`
            );
        }
    } catch (error) {
        logger.error('Error enhancing service categories data:', error);
        // Continue with static data if enhancement fails
    }
};

/**
 * Get sections by type
 *
 * @param sections - Array of sections to filter
 * @param type - Type of sections to filter for
 * @returns Array of sections matching the specified type
 */
export const getSectionsByType = (
    sections: SectionData[],
    type: string
): SectionData[] => {
    return sections.filter((section) => section.type === type);
};

/**
 * Get header section
 *
 * @param sections - Array of sections to search
 * @returns Header section or undefined if not found
 */
export const getHeaderSection = (
    sections: SectionData[]
): SectionData | undefined => {
    return sections.find((section) => section.type === 'header');
};

/**
 * Get footer section
 *
 * @param sections - Array of sections to search
 * @returns Footer section or undefined if not found
 */
export const getFooterSection = (
    sections: SectionData[]
): SectionData | undefined => {
    return sections.find((section) => section.type === 'footer');
};

// Export the service functions as an object
export const pageDataService = {
    getPageData,
    getSectionsByType,
    getHeaderSection,
    getFooterSection,
};
