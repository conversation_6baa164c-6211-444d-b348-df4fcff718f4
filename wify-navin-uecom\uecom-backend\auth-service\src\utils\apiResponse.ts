import { getReasonPhrase } from 'http-status-codes';

export const buildAPIResponse = (
    statusCode: number,
    message: string,
    data: any = null,
    error: any = null
) => {
    const statusMessage = getReasonPhrase(statusCode);

    return {
        statusCode,
        statusMessage,
        message,
        data,
        error,
    };
};

export const buildExtApiResponse = (
    statusCode: number,
    message: string,
    data: any = null,
    error: any = null
) => {
    const statusMessage = getReasonPhrase(statusCode);

    return {
        statusCode,
        statusMessage,
        message,
        data,
        error,
    };
};
