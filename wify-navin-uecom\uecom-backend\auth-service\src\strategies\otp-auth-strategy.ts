/**
 * OTP Authentication Strategy
 *
 * This class implements the authentication strategy interface for OTP-based authentication.
 * It follows the Strategy pattern to allow for different authentication methods.
 *
 * @module strategies/otp-auth-strategy
 */

import { IAuthStrategy } from '../interfaces/auth-strategy.interface';
import { IOtpStorage } from '../interfaces/otp-storage.interface';
import { ISmsAdapter } from '../adapters/sms-adapter.interface';
import {
    OtpRequest,
    OtpVerificationRequest,
    ServiceResponse,
    OtpServiceResponse,
    VerificationResponse,
} from '../types/auth.types';
import { StatusCodes } from 'http-status-codes';
import crypto from 'crypto';
import { EnvironmentUtils, STATIC_OTP } from '../utils/environment.utils';

export class OtpAuthStrategy implements IAuthStrategy {
    /**
     * Creates a new instance of the OtpAuthStrategy
     *
     * @param {IOtpStorage} otpStorage - The storage service for OTPs
     * @param {ISmsAdapter} smsAdapter - The adapter for sending SMS messages
     */
    constructor(
        private readonly otpStorage: IOtpStorage,
        private readonly smsAdapter: ISmsAdapter
    ) {}

    /**
     * Initiates the OTP authentication process
     *
     * @param {OtpRequest} params - Object containing the mobile number
     * @returns {Promise<ServiceResponse<OtpServiceResponse>>} Response with success status and data or error
     */
    async initiate({
        mobile,
    }: OtpRequest): Promise<ServiceResponse<OtpServiceResponse>> {
        try {
            // Validate mobile number format
            if (
                !mobile ||
                typeof mobile !== 'string' ||
                !/^\d{10,15}$/.test(mobile)
            ) {
                return {
                    success: false,
                    error: {
                        message: 'Invalid mobile number format',
                        code: 'INVALID_MOBILE_FORMAT',
                    },
                };
            }

            // Generate a random 6-digit OTP
            const otp = this.generateOtp();

            // Create a secure message with the OTP and expiration information
            const message = `Use ${otp} as your login OTP for WIFY APP`;

            // Send the OTP via SMS using the injected SMS adapter
            const smsResponse = await this.smsAdapter.sendSMS({
                to: mobile,
                message,
            });

            // Handle SMS sending failure
            if (smsResponse.statusCode !== StatusCodes.OK) {
                return {
                    success: false,
                    error: {
                        message: smsResponse.message || 'Failed to send OTP',
                        code: smsResponse.error?.code || 'SMS_SEND_FAILED',
                    },
                };
            }

            // Store the OTP using the injected storage service with 15 minutes expiration
            await this.otpStorage.storeOtp(mobile, otp, 15);

            // Mask the mobile number for the response
            const maskedMobile = this.maskMobileNumber(mobile);

            // Return success response WITHOUT the OTP for security
            return {
                success: true,
                data: {
                    message: `OTP sent successfully to ${maskedMobile}`,
                    mobile: maskedMobile,
                    // OTP removed for security
                },
            };
        } catch (error) {
            console.error('Error in OTP initiation:', error);

            // Handle any unexpected errors
            return {
                success: false,
                error: {
                    message: 'Failed to process OTP request',
                    code: 'OTP_REQUEST_FAILED',
                },
            };
        }
    }

    /**
     * Masks a mobile number for security in responses
     *
     * @private
     * @param {string} mobile - The mobile number to mask
     * @returns {string} Masked mobile number (e.g., "******1234")
     */
    private maskMobileNumber(mobile: string): string {
        if (mobile.length <= 4) {
            return '****';
        }
        return '*'.repeat(mobile.length - 4) + mobile.slice(-4);
    }

    /**
     * Verifies the OTP submitted by a user
     *
     * @param {OtpVerificationRequest} params - Object containing mobile number and OTP
     * @returns {Promise<ServiceResponse<VerificationResponse>>} Response with verification result
     */
    async verify({
        mobile,
        otp,
    }: OtpVerificationRequest): Promise<ServiceResponse<VerificationResponse>> {
        try {
            console.log(`🔍 Verifying OTP for mobile: ${mobile}, OTP: ${otp}`);

            // Retrieve the stored OTP using the injected storage service
            const storedOtp = await this.otpStorage.getOtp(mobile);
            console.log(
                `📱 Stored OTP for ${mobile}: ${storedOtp || 'Not found'}`
            );

            // Verify the OTP matches
            if (!storedOtp) {
                console.log(`❌ No OTP found for mobile: ${mobile}`);
                return {
                    success: false,
                    error: {
                        message: 'Invalid OTP or mobile number',
                        code: 'INVALID_OTP',
                    },
                };
            }

            if (storedOtp !== otp) {
                console.log(
                    `❌ OTP mismatch for mobile: ${mobile}. Expected: ${storedOtp}, Received: ${otp}`
                );
                return {
                    success: false,
                    error: {
                        message: 'Invalid OTP or mobile number',
                        code: 'INVALID_OTP',
                    },
                };
            }

            console.log(`✅ OTP verified successfully for mobile: ${mobile}`);

            // Remove the OTP from storage after successful verification
            await this.otpStorage.removeOtp(mobile);
            console.log(`🗑️ OTP removed from storage for mobile: ${mobile}`);

            // Return success response
            return {
                success: true,
                data: {
                    message: `Mobile number ${mobile} verified successfully`,
                },
            };
        } catch (error) {
            console.error('Error in OTP verification:', error);

            // Handle any unexpected errors
            return {
                success: false,
                error: {
                    message: 'Failed to process OTP verification',
                    code: 'OTP_VERIFICATION_FAILED',
                },
            };
        }
    }

    /**
     * Generates a cryptographically secure random 6-digit OTP
     * In non-production environments, returns a static OTP for easier testing
     *
     * @private
     * @returns {string} 6-digit OTP
     */
    private generateOtp(): string {
        // In non-production environments, use a static OTP for easier testing
        if (EnvironmentUtils.shouldUseStaticOtp()) {
            console.log(
                `🔑 Using static OTP (${STATIC_OTP}) for ${EnvironmentUtils.getEnvironment()} environment`
            );
            return STATIC_OTP;
        }

        // For production, generate a random number between 100000 and 999999 (6 digits)
        // This ensures we always get a 6-digit number that doesn't start with 0
        const min = 100000; // Smallest 6-digit number
        const max = 999999; // Largest 6-digit number

        // Generate a cryptographically secure random integer in the range [min, max]
        const randomBuffer = crypto.randomBytes(4); // 4 bytes = 32 bits, enough for our range
        const randomNumber = randomBuffer.readUInt32BE(0);

        // Scale the random number to our desired range
        const scaledRandom = min + (randomNumber % (max - min + 1));

        return scaledRandom.toString();
    }
}
