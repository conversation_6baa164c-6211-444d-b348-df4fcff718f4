'use client'

import React from 'react'
import { AlertTriangle } from 'lucide-react'
import { useStaticText } from '@/hooks/useStaticText'

interface ErrorFallbackProps {
    /**
     * Title of the error message
     * @default 'Something went wrong'
     */
    title?: string

    /**
     * Error message to display
     * @default 'We're sorry, but there was an error loading this content. Please try refreshing the page.'
     */
    message?: string

    /**
     * Optional retry function
     */
    onRetry?: () => void
}

/**
 * Error fallback component to display when an error occurs
 * Can be used with ErrorBoundary or for API error states
 */
const ErrorFallback: React.FC<ErrorFallbackProps> = ({
    title = 'Something went wrong',
    message = "We're sorry, but there was an error loading this content. Please try refreshing the page.",
    onRetry,
}) => {
    // Default fallback texts
    const fallbackTitle = 'Something went wrong'
    const fallbackMessage =
        "We're sorry, but there was an error loading this content. Please try refreshing the page."
    const fallbackRetry = 'Try again'

    // Initialize with provided props or fallbacks
    let errorTitle = title || fallbackTitle
    let errorMessage = message || fallbackMessage
    let retryText = fallbackRetry

    // Get static text
    const { t } = useStaticText()

    // Use static text with fallbacks
    if (!title) {
        errorTitle = t('errors.server_error') || fallbackTitle
    }

    if (!message) {
        errorMessage = t('errors.try_again') || fallbackMessage
    }

    retryText = t('errors.try_again') || fallbackRetry
    return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-md text-red-800 max-w-2xl mx-auto my-4">
            <div className="flex items-center gap-3 mb-3">
                <AlertTriangle className="h-6 w-6 text-red-500" />
                <h2 className="text-lg font-semibold">{errorTitle}</h2>
            </div>

            <p className="mb-4">{errorMessage}</p>

            {onRetry && (
                <button
                    onClick={onRetry}
                    className="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-800 rounded-md transition-colors"
                    aria-label={retryText}
                >
                    {retryText}
                </button>
            )}
        </div>
    )
}

export default ErrorFallback
