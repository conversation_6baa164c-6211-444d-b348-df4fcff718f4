{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[javascript]": {"editor.formatOnSave": true}, "[typescript]": {"editor.formatOnSave": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/node_modules": true, "**/.next": true, "**/dist": true, "**/coverage": true}, "search.exclude": {"**/node_modules": true, "**/.next": true, "**/dist": true, "**/coverage": true}, "editor.rulers": [100], "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "javascript.preferences.importModuleSpecifier": "non-relative", "typescript.preferences.importModuleSpecifier": "non-relative", "tailwindCSS.includeLanguages": {"typescript": "javascript", "typescriptreact": "javascript"}, "tailwindCSS.experimental.classRegex": [["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]]}