import SplashScreen from '@/components/ui/organisms/SplashScreen'

async function getData() {
    const res = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/screens`, {
        cache: 'no-store', // Prevents caching, behaves like getServerSideProps
    })

    if (!res.ok) throw new Error('Failed to fetch screens')

    return res.json()
}

export default async function Home() {
    const screens = await getData()
    return <SplashScreen screens={screens} />
}
