'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { resendOTP, verifyOTP } from '@/app/api/auth'
import { EnvironmentUtils, STATIC_OTP } from '@/utils/environment.utils'

export default function OTPVerification() {
    const [phone, setPhone] = useState<string>('')
    const [dialCode, setDialCode] = useState<string>('')
    const router = useRouter()

    const [otp, setOtp] = useState<string[]>(new Array(6).fill(''))
    const [resendTimer, setResendTimer] = useState<number>(30)
    const [canResend, setCanResend] = useState<boolean>(false)
    const [isVerified, setIsVerified] = useState<boolean>(false)
    const [errorMessage, setErrorMessage] = useState<string>('')
    const otpRefs = useRef<HTMLInputElement[]>([])

    useEffect(() => {
        otpRefs.current[0]?.focus()

        const storedPhone = localStorage.getItem('phone') || ''
        const storedDialCode = localStorage.getItem('dialCode') || ''
        setPhone(storedPhone)
        setDialCode(storedDialCode)

        const verified = localStorage.getItem('isVerified') === 'true'
        if (verified) {
            setIsVerified(true)
            router.push('/')
        }
    }, [])

    useEffect(() => {
        if (resendTimer > 0) {
            const timer = setInterval(() => setResendTimer((prev) => prev - 1), 1000)
            return () => clearInterval(timer)
        }
        setCanResend(true)
    }, [resendTimer])

    useEffect(() => {
        setTimeout(() => {
            if (!EnvironmentUtils.isProduction()) {
                const staticOtpArray = STATIC_OTP.split('')
                setOtp(staticOtpArray)
            }
        }, 1500)
    }, [])

    const handleOtpChange = (index: number, value: string) => {
        if (!/^\d*$/.test(value)) return
        const newOtp = [...otp]
        newOtp[index] = value
        setOtp(newOtp)

        if (index < 5 && value !== '') {
            otpRefs.current[index + 1]?.focus()
        }
    }

    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace' && otp[index] === '' && index > 0) {
            otpRefs.current[index - 1]?.focus()
        }
    }

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
        e.preventDefault()
        const pastedData = e.clipboardData.getData('text').trim()
        if (/^\d{6}$/.test(pastedData)) {
            setOtp(pastedData.split(''))
            otpRefs.current[5]?.focus()
        }
    }

    const handleVerifyOTP = async () => {
        const otpCode = otp.join('')
        if (otpCode.length < 6) {
            setErrorMessage('❌ Please enter a valid 6-digit OTP.')
            return
        }

        try {
            console.log('Verifying OTP:', { phone, otpCode })
            const response = await verifyOTP(phone, otpCode)
            console.log('OTP verification response:', response)

            if (response && response.statusCode >= 200 && response.statusCode < 300) {
                setIsVerified(true)

                // Set authentication state in localStorage
                localStorage.setItem('isLoggedIn', 'true')
                localStorage.setItem('isVerified', 'true')

                // Set authentication state in cookies for server-side middleware
                document.cookie = 'isVerified=true; path=/; max-age=86400' // 24 hours

                // Redirect to discovery page
                router.push('/discovery')
            } else {
                // Handle API error response
                const errorMsg = response?.message || 'OTP verification failed'
                setErrorMessage(`❌ ${errorMsg}`)
                setOtp(new Array(6).fill(''))
                otpRefs.current[0]?.focus()
            }
        } catch (error: any) {
            console.error('OTP verification error:', error)
            setErrorMessage(`❌ ${error.message}`)
            setOtp(new Array(6).fill(''))
            otpRefs.current[0]?.focus()
        }
    }

    const handleResendOTP = async () => {
        try {
            await resendOTP(phone)
            setResendTimer(30)
            setCanResend(false)
        } catch (error: any) {
            setErrorMessage(`❌ ${error.message}`)
        }
    }

    return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-gray-100 px-4">
            <button
                onClick={() => router.push('/auth')}
                className="absolute top-5 left-5 text-2xl text-gray-600"
            >
                ←
            </button>

            <h2 className="text-2xl font-bold text-brand-blue-primary text-center">
                Verify Your Phone Number
            </h2>
            <p className=" text-sm mt-1 text-brand-blue-primary">Verification code has been sent</p>
            <p className=" text-sm  text-brand-blue-primary">
                on {dialCode}-{phone}
            </p>
            <div className="mt-8 w-48 h-48 flex items-center justify-center relative">
                <div className="rounded-full bg-gradient-to-b from-white to-[#DCEAFF] overflow-hidden">
                    <Image
                        src="/assets/images/otp_page_img.png"
                        width={192}
                        height={192}
                        alt="Phone Authentication"
                        className="object-cover scale-125"
                        loading="lazy"
                    />
                    <div className="text-xs absolute top-[105px] left-[75px] text-white bg-[#5392E6] items-center justify-center h-4 p-1 px-3 rounded-full">
                        ****
                    </div>
                </div>
            </div>

            <div className="mt-6 flex gap-2">
                {otp.map((digit, index) => (
                    <input
                        key={index}
                        ref={(el) => {
                            otpRefs.current[index] = el as HTMLInputElement
                        }}
                        type="text"
                        autoFocus
                        value={digit}
                        onChange={(e) => handleOtpChange(index, e.target.value.slice(0, 1))}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                        onPaste={handlePaste}
                        maxLength={1}
                        className="w-10 h-12 text-center text-xl font-semibold border border-gray-400 rounded-md focus:outline-none focus:border-blue-500 text-black"
                    />
                ))}
            </div>

            <div className="mt-3 text-brand-blue-primary">
                {canResend ? (
                    <button onClick={handleResendOTP} className="text-blue-500 font-semibold">
                        Resend OTP
                    </button>
                ) : (
                    <p className="text-brand-blue-primary text-sm">
                        Resend OTP in
                        <span className="text-[#DF1C03]"> {resendTimer} seconds</span>{' '}
                    </p>
                )}
            </div>

            <button
                onClick={handleVerifyOTP}
                disabled={otp.join('').length < 6}
                className={`mt-6 py-3 w-full max-w-sm text-white font-semibold rounded-lg shadow-md transition duration-300 ${
                    otp.join('').length === 6
                        ? 'bg-[#162D50] hover:bg-[#0F1E3C]'
                        : 'bg-gray-400 cursor-not-allowed'
                }`}
            >
                Verify & Proceed
            </button>

            {errorMessage && !isVerified && <p className="mt-3 text-red-500">{errorMessage}</p>}
            {isVerified && <p className="mt-3 text-green-500">✅ OTP Verified Successfully!</p>}
        </div>
    )
}
