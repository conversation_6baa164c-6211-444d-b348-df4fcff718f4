'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import Head from 'next/head'
import PhoneInput from '@/components/PhoneInput'
import { requestOTP } from '../api/auth'

function SignInComponent() {
    const [phone, setPhone] = useState<string | undefined>(undefined)
    const router = useRouter()

    useEffect(() => {
        const phoneNumber = localStorage.getItem('phone') || undefined
        setPhone(phoneNumber)

        if (localStorage.getItem('isVerified') === 'true') {
            router.push('/discovery')
        }
    }, [])

    const handleSendOTP = async () => {
        if (!phone || phone.toString().length !== 10) {
            alert('Please enter a valid 10-digit phone number.')
            return
        }

        try {
            if (!phone) {
                throw new Error('Phone number is not valid')
            }
            const data = await requestOTP(phone)
            localStorage.setItem('phone', phone.toString())
            localStorage.setItem('dialCode', '+91')
            console.log('OTP sent successfully:', data)
            router.push('/auth/otp-verification')
        } catch (error: any) {
            console.error('Error sending OTP:', error.message)
            alert(error.message || 'Something went wrong. Please try again.')
        }
    }

    return (
        <>
            <Head>
                <title>Sign In | My eCommerce</title>
                <meta
                    name="description"
                    content="Sign in with your phone number to access your account."
                />
            </Head>

            <div className="flex flex-col items-center justify-center min-h-screen px-4">
                <h2 className="text-2xl font-bold text-brand-blue-primary">Continue With Phone</h2>
                <p className="text-sm mt-1 text-brand-blue-primary font-medium">We will send OTP</p>

                <div className="mt-8 w-48 h-48 flex items-center justify-center relative">
                    <div className="rounded-full bg-gradient-to-b from-white to-[#DCEAFF] overflow-hidden">
                        <Image
                            src="/assets/images/login_page_img.png"
                            width={192}
                            height={192}
                            alt="Phone Authentication"
                            className="object-cover scale-125"
                            loading="lazy"
                        />
                        <div className="border-0 px-4  flex absolute top-12 left-6 text-[7.88px] font-bold text-center text-white bg-[#5392E6] p-2 rounded-full">
                            Phone Number
                        </div>
                    </div>

                    <div className="flex absolute top-5 w-20 h-12 left-36">
                        <img
                            src="/assets/images/chat.png"
                            alt="Phone Authentication"
                            className="w-full h-full object-fill"
                        />
                    </div>
                </div>

                <div className="mt-8 w-full max-w-sm">
                    <label className="block text-[#2A3C5C99] text-sm font-normal mb-1">
                        Enter your phone number<span className="text-red-500">*</span>
                    </label>
                    <PhoneInput value={phone} onChange={setPhone} />
                </div>

                <button
                    onClick={handleSendOTP}
                    disabled={phone?.toString().length !== 10}
                    className="h-10 mt-6 bg-brand-blue-primary text-white font-semibold rounded-lg w-full max-w-sm shadow-md
                                hover:bg-[#0F1E3C] transition duration-300
                                disabled:bg-[#FAFAFA]
                                disabled:cursor-not-allowed
                                disabled:text-brand-blue-primary
                                disabled:border-brand-blue-primary
                                disabled:border
                                disabled:opacity-50"
                >
                    Get OTP
                </button>
            </div>
        </>
    )
}

// Use dynamic import to ensure client-side only rendering
import dynamic from 'next/dynamic'
import { inherits } from 'util'

const SignIn = dynamic(() => Promise.resolve(SignInComponent), {
    ssr: false,
    loading: () => (
        <div className="flex items-center justify-center min-h-screen bg-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
    ),
})

export default SignIn
