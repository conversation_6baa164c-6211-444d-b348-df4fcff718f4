{"version": "2.0.0", "tasks": [{"label": "Start Frontend (Next.js)", "type": "shell", "command": "yarn --cwd wify-navin-uecom/uecom-frontend dev", "presentation": {"reveal": "always", "panel": "new", "focus": false, "group": "servers"}, "problemMatcher": [], "runOptions": {"runOn": "folderOpen"}}, {"label": "Start App Service (Backend)", "type": "shell", "command": "yarn --cwd wify-navin-uecom/uecom-backend/app-service dev", "presentation": {"reveal": "always", "panel": "new", "focus": false, "group": "servers"}, "problemMatcher": []}, {"label": "Start Auth Service (Backend)", "type": "shell", "command": "yarn --cwd wify-navin-uecom/uecom-backend/auth-service dev", "presentation": {"reveal": "always", "panel": "new", "focus": false, "group": "servers"}, "problemMatcher": []}, {"label": "Start All Servers", "dependsOn": ["Start Frontend (Next.js)", "Start App Service (Backend)", "Start Auth Service (Backend)"], "dependsOrder": "parallel", "group": {"kind": "build", "isDefault": true}, "presentation": {"reveal": "always", "panel": "new"}}]}