{"version": "2.0.0", "tasks": [{"label": "Start React Admin", "type": "shell", "command": "yarn --cwd react-admin dev", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Start Node <PERSON>end", "type": "shell", "command": "yarn --cwd node-backend dev", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Start Next Client", "type": "shell", "command": "yarn --cwd next-client dev", "presentation": {"reveal": "always", "panel": "new", "focus": true}, "problemMatcher": []}, {"label": "Start All Servers", "dependsOn": ["Start React Admin", "Start Node <PERSON>end", "Start Next Client"], "group": {"kind": "build", "isDefault": true}}]}