// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model version_check {
  id                             Int     @id @default(autoincrement())
  platform                       String  @unique
  current_version                String
  stable_version                 String
  stable_message                 String?
  critical_message               String?
  stable_title                   String?
  critical_title                 String?
  download_url                   String
  is_playstore_immediate_release Boolean @default(false)
  version_helper_visibility      Boolean @default(true)
  show_playstore_release         Boolean @default(false)
}

model Consumer {
  consumer_id   String    @id @default(uuid()) @db.Uuid
  name          String?   @db.VarChar(500)
  email         String?   @db.VarChar(50)
  mobile_no     String    @unique @db.VarChar(10)
  otp           Int?
  otp_exp       DateTime?
  profile_image String?   @db.VarChar(500)
  is_active     Boolean   @default(true)
  org_id        String?   @db.Uuid
  c_ip          String?   @db.VarChar(100)
  c_user_agent  String?   @db.VarChar(500)
  c_time        DateTime?
  u_ip          String?   @db.VarChar(100)
  u_user_agent  String?   @db.VarChar(500)
  u_time        DateTime?

  @@map("cl_tx_consumer")
}

model UserLogins {
  userId       String    @db.Uuid
  loginNo      Int       @default(autoincrement())
  indType      String    @db.VarChar(100)
  userType     String?   @db.VarChar(20)
  hash         String?   @db.VarChar(100)
  lastSeen     DateTime? @default(now())
  indId        String?   @db.VarChar(100)
  c_ip         String?   @db.VarChar(100)
  c_user_agent String?   @db.VarChar(500)
  c_time       DateTime?
  u_ip         String?   @db.VarChar(100)
  u_user_agent String?   @db.VarChar(500)
  u_time       DateTime?

  @@id([userId, loginNo])
  @@map("cl_tx_usr_logins")
}

model UserIdentities {
  dbId         String    @id @default(uuid()) @db.Uuid
  usrId        String?   @db.Uuid
  identityType String?   @db.Uuid
  id           String?
  key          String?
  token        String?
  orgId        String?   @db.Uuid
  c_ip         String?   @db.VarChar(100)
  c_user_agent String?   @db.VarChar(500)
  c_time       DateTime?
  u_ip         String?   @db.VarChar(100)
  u_user_agent String?   @db.VarChar(500)
  u_time       DateTime?

  identityTypeRelation SysCfIdentity? @relation("SysCfIdentityToUserIdentities", fields: [identityType], references: [identityId])

  @@index([usrId])
  @@map("cl_tx_usr_identities")
}

model SysCfIdentity {
  identityId String  @id @default(uuid()) @db.Uuid
  type       String? @db.VarChar(20)

  UserIdentities UserIdentities[] @relation("SysCfIdentityToUserIdentities")

  @@map("sys_cf_identity")
}
