/**
 * Dependency Injection Setup
 *
 * This module sets up the dependency injection container for the application.
 * It registers all services, repositories, and factories with the container.
 *
 * @module di/setup
 */

import { container } from './container';
import { SERVICE_TOKENS } from './tokens';

// Config
import { createConfigService, ConfigService } from '../configs/config.service';

// Repositories
import {
    createRedisRepository,
    IRedisRepository,
} from '../repositories/redis.repository';
import { createOtpRepository } from '../repositories/otp.repository';

// Factories
import { AuthStrategyFactory } from '../factories/auth-strategy-factory';
import { SmsAdapterFactory } from '../factories/sms-adapter-factory';

// Services
import { jwtService } from '../services/jwt.service';
import { cookieService } from '../services/cookie.service';
import { databaseService } from '../services/database.service';
import { initAuthService } from '../services/auth-service-init';
import { getAuthServiceInstance } from '../services/auth';
import { CustomHttpService } from '../services/http';
import { createTransactionService } from '../services/transaction.service';
import { createDatabaseHealthService } from '../services/database-health.service';
import { createDistributedTransactionService } from '../services/distributed-transaction.service';

/**
 * Sets up the dependency injection container
 */
export function setupContainer(): void {
    // Register config service
    container.register(SERVICE_TOKENS.CONFIG_SERVICE, new ConfigService());

    // Register repositories
    container.registerFactory(
        SERVICE_TOKENS.REDIS_SERVICE,
        createRedisRepository
    );
    // Also register as REDIS_REPOSITORY for health checks
    container.registerFactory(
        SERVICE_TOKENS.REDIS_REPOSITORY,
        createRedisRepository
    );
    container.registerFactory(SERVICE_TOKENS.OTP_STORAGE, createOtpRepository);

    // Register factories
    container.registerFactory(
        SERVICE_TOKENS.AUTH_STRATEGY_FACTORY,
        () => AuthStrategyFactory
    );

    // Register services
    container.register(SERVICE_TOKENS.TOKEN_SERVICE, jwtService);
    container.register(SERVICE_TOKENS.COOKIE_SERVICE, cookieService);
    container.register(SERVICE_TOKENS.DATABASE_SERVICE, databaseService);

    // Register transaction services
    container.registerFactory(SERVICE_TOKENS.TRANSACTION_SERVICE, async () => {
        const prisma = await databaseService.getPrismaClient();
        return createTransactionService(prisma);
    });
    container.registerFactory(
        SERVICE_TOKENS.DISTRIBUTED_TRANSACTION_SERVICE,
        createDistributedTransactionService
    );

    // Register database health service
    container.registerFactory(
        SERVICE_TOKENS.DATABASE_HEALTH_SERVICE,
        async () => {
            const prisma = await databaseService.getPrismaClient();
            return createDatabaseHealthService(prisma, {
                failureThreshold: 3,
                resetTimeout: 30000,
                checkInterval: 60000,
            });
        }
    );

    // Register HTTP service
    container.register(SERVICE_TOKENS.HTTP_SERVICE, new CustomHttpService());

    // Register SMS adapter
    container.registerFactory(SERVICE_TOKENS.SMS_SERVICE, () =>
        SmsAdapterFactory.getDefaultAdapter()
    );

    // Register auth strategy
    container.registerFactory(SERVICE_TOKENS.AUTH_STRATEGY, () =>
        AuthStrategyFactory.getDefaultStrategy()
    );

    // Initialize auth service
    initAuthService();

    // Register auth service
    container.register(SERVICE_TOKENS.AUTH_SERVICE, getAuthServiceInstance());

    console.log('✅ Dependency injection container set up successfully');
}
