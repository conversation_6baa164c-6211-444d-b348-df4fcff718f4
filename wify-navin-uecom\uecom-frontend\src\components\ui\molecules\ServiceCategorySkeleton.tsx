/**
 * Service Category Skeleton Component
 * Loading placeholder for service category items using existing SkeletonLoader
 */

import React from 'react';
import SkeletonLoader from '../atoms/SkeletonLoader';
import { useDeviceContext } from '../../../providers/DeviceProvider';

interface ServiceCategorySkeletonProps {
    /**
     * Number of skeleton items to show
     */
    count?: number;
    
    /**
     * Additional CSS classes
     */
    className?: string;
}

/**
 * Single Service Category Skeleton Item
 */
const ServiceCategorySkeletonItem: React.FC = () => {
    return (
        <div className="bg-gray-100 p-4 rounded-xl shadow-sm border border-gray-200 relative h-[120px] overflow-hidden">
            {/* Icon Skeleton */}
            <div className="absolute -top-2 -right-2">
                <div className="bg-gray-300 w-16 h-16 rounded-full flex items-center justify-center p-2">
                    <SkeletonLoader variant="circle" ariaLabel="Loading service category icon" />
                </div>
            </div>
            
            {/* Title Skeleton */}
            <div className="absolute bottom-1 left-2">
                <SkeletonLoader variant="text" className="h-4 w-20 mb-1" ariaLabel="Loading service category title" />
            </div>
        </div>
    );
};

/**
 * Service Category Grid Skeleton
 */
const ServiceCategorySkeleton: React.FC<ServiceCategorySkeletonProps> = ({
    count = 6,
    className = '',
}) => {
    const { isMobile, isTablet } = useDeviceContext();
    
    // Determine grid layout based on device
    const getGridClasses = () => {
        if (isMobile) return 'grid-cols-3 gap-3';
        if (isTablet) return 'grid-cols-4 gap-4';
        return 'grid-cols-6 gap-4';
    };
    
    return (
        <div className={`grid ${getGridClasses()} ${className}`}>
            {Array.from({ length: count }).map((_, index) => (
                <ServiceCategorySkeletonItem key={index} />
            ))}
        </div>
    );
};

/**
 * Service Category Section Skeleton
 * Complete skeleton for the entire service category section
 */
export const ServiceCategorySectionSkeleton: React.FC = () => {
    const { isMobile, isTablet } = useDeviceContext();
    
    const sectionClasses = isMobile 
        ? 'px-4 mt-8' 
        : isTablet 
        ? 'px-6 mt-6' 
        : 'px-10 mt-2';
    
    return (
        <section className={sectionClasses}>
            <div className="mb-4">
                {/* Section Title Skeleton */}
                <SkeletonLoader
                    variant="text"
                    className={`h-5 mb-4 ${
                        isMobile ? 'w-48' : isTablet ? 'w-56' : 'w-64'
                    }`}
                    ariaLabel="Loading section title"
                />
                
                {/* Service Categories Grid Skeleton */}
                <ServiceCategorySkeleton 
                    count={isMobile ? 6 : isTablet ? 8 : 6}
                />
            </div>
        </section>
    );
};

export default ServiceCategorySkeleton;
