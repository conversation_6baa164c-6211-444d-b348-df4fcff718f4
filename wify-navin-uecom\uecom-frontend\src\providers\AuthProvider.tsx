'use client'

import React, { createContext, useContext, ReactNode } from 'react'
import { useAuth, User } from '../hooks/useAuth'

// Define auth context type
interface AuthContextType {
    user: User | null
    isLoading: boolean
    isAuthenticated: boolean
    error: string | null
    login: (email: string, password: string) => Promise<boolean>
    logout: () => Promise<void>
    register: (name: string, email: string, password: string) => Promise<boolean>
}

// Create auth context with default values
const AuthContext = createContext<AuthContextType>({
    user: null,
    isLoading: false,
    isAuthenticated: false,
    error: null,
    login: async () => false,
    logout: async () => {},
    register: async () => false,
})

// Custom hook for using auth context
export const useAuthContext = () => useContext(AuthContext)

// Auth provider props
interface AuthProviderProps {
    children: ReactNode
}

/**
 * Auth provider component
 * Provides authentication state and functions to all children
 *
 * @param children - Child components
 *
 * @example
 * ```tsx
 * <AuthProvider>
 *   <App />
 * </AuthProvider>
 * ```
 */
export function AuthProvider({ children }: AuthProviderProps) {
    const auth = useAuth()

    return <AuthContext.Provider value={auth}>{children}</AuthContext.Provider>
}
