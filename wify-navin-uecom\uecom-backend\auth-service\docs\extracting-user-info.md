# Extracting User Information from <PERSON>WT Tokens

This guide explains how to extract user information from JWT tokens in the WIFY ECOM application.

## Table of Contents

1. [Introduction](#introduction)
2. [JWT Token Structure](#jwt-token-structure)
3. [Extracting User ID](#extracting-user-id)
4. [Fetching User Data](#fetching-user-data)
5. [Client-Side Token Handling](#client-side-token-handling)
6. [Security Considerations](#security-considerations)

## Introduction

JWT (JSON Web Tokens) are used for authentication in the WIFY ECOM application. These tokens contain encoded information about the user, including their unique identifier (user ID). This guide explains how to extract and use this information.

## JWT Token Structure

A JWT token consists of three parts separated by dots (`.`):

1. **Header**: Contains the token type and signing algorithm
2. **Payload**: Contains the claims (data) about the user
3. **Signature**: Verifies the token hasn't been tampered with

In our application, the payload contains the following claims:

- `sub`: The subject (user ID)
- `type`: The token type (access, refresh, or session)
- `jti`: A unique token identifier
- `iat`: Issued at timestamp
- `exp`: Expiration timestamp

## Extracting User ID

### Server-Side (Using Middleware)

When using the authentication middleware, the user ID is automatically extracted and attached to the request object:

```typescript
// In a protected route handler
router.get('/protected-route', authenticateWithCookie, (req, res) => {
  // The user ID is available in req.user.id
  const userId = req.user.id;
  
  // Use the user ID as needed
  res.json({ message: 'Protected route accessed', userId });
});
```

### Server-Side (Manual Verification)

If you need to manually verify a token and extract the user ID:

```typescript
import { jwtService, TokenType } from '../services/jwt.service';

// Verify a token and extract the user ID
try {
  const token = 'your-jwt-token';
  const decoded = jwtService.verifyToken(token, TokenType.ACCESS);
  
  // The user ID is in the 'sub' claim
  const userId = decoded.sub;
  
  // Use the user ID as needed
} catch (error) {
  // Handle verification errors
  console.error('Token verification failed:', error);
}
```

## Fetching User Data

Once you have the user ID, you can fetch additional user data from the database:

```typescript
import { databaseService } from '../services/database.service';

// In a protected route handler
const userId = req.user.id;

// Fetch user data from the database
try {
  const user = await databaseService.findUserById(userId);
  
  if (user) {
    // User data is available
    const { consumer_id, name, email, mobile_no, profile_image, is_active } = user;
    
    // Use the user data as needed
    res.json({
      id: consumer_id,
      name,
      email,
      mobile: mobile_no,
      profileImage: profile_image,
      isActive: is_active,
    });
  } else {
    // User not found
    res.status(404).json({ message: 'User not found' });
  }
} catch (error) {
  // Handle database errors
  console.error('Error fetching user data:', error);
  res.status(500).json({ message: 'Error fetching user data' });
}
```

## Client-Side Token Handling

On the client side, you can decode the JWT token to extract user information:

```javascript
// Using a library like jwt-decode
import jwtDecode from 'jwt-decode';

// Decode the token
const token = localStorage.getItem('accessToken');
const decoded = jwtDecode(token);

// The user ID is in the 'sub' claim
const userId = decoded.sub;

// Check if the token is expired
const isExpired = decoded.exp < Date.now() / 1000;

if (isExpired) {
  // Token is expired, refresh it or redirect to login
} else {
  // Token is valid, use the user ID
}
```

**Important**: Client-side decoding is for convenience only. Always verify tokens on the server side for security.

## Security Considerations

1. **Never trust client-side token validation**: Always verify tokens on the server side
2. **Keep tokens secure**: Store tokens in secure HTTP-only cookies for web applications
3. **Use appropriate token expiry times**: Short-lived access tokens, longer-lived refresh tokens
4. **Implement token refresh**: Automatically refresh tokens before they expire
5. **Revoke tokens when needed**: Implement token revocation for logout or security incidents
6. **Use HTTPS**: Always use HTTPS to protect tokens in transit
7. **Validate token types**: Ensure tokens are used for their intended purpose (access, refresh, session)
8. **Handle errors gracefully**: Provide appropriate error messages without revealing sensitive information
