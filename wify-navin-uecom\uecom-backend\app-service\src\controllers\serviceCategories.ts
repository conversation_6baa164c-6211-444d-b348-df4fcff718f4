/**
 * Service Categories Controller
 *
 * This controller handles HTTP requests for service category data.
 * It follows the same pattern as the brands controller for consistency.
 */

import { Request, Response } from 'express';
import { serviceCategoriesService } from '../services/serviceCategories.service';
import { logger } from '../utils/logger';

/**
 * Get all service categories
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const getAllServiceCategories = async (req: Request, res: Response): Promise<void> => {
    try {
        logger.info('Fetching all service categories');

        // Get service categories from the service
        const serviceCategoriesData = await serviceCategoriesService.getAllServiceCategories();

        // Log the response
        logger.info(`Successfully fetched ${serviceCategoriesData.categories.length} service categories`);

        // Send the response
        res.status(200).json({
            success: true,
            data: serviceCategoriesData.categories,
            meta: serviceCategoriesData.meta,
            timestamp: new Date().toISOString(),
        });
    } catch (error) {
        logger.error('Error fetching service categories:', error);

        // Send error response
        res.status(500).json({
            success: false,
            error: 'Failed to fetch service categories',
            message: error instanceof Error ? error.message : 'Unknown error occurred',
            timestamp: new Date().toISOString(),
        });
    }
};

/**
 * Clear service categories cache
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const clearServiceCategoriesCache = async (req: Request, res: Response): Promise<void> => {
    try {
        logger.info('Clearing service categories cache');

        // Clear the cache by creating a new service instance
        // Note: In a production environment, you might want to implement a proper cache clearing mechanism
        
        res.status(200).json({
            success: true,
            message: 'Service categories cache cleared successfully',
            timestamp: new Date().toISOString(),
        });
    } catch (error) {
        logger.error('Error clearing service categories cache:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to clear cache',
            message: error instanceof Error ? error.message : 'Unknown error occurred',
            timestamp: new Date().toISOString(),
        });
    }
};

/**
 * Get service categories cache status
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const getServiceCategoriesCacheStatus = async (req: Request, res: Response): Promise<void> => {
    try {
        logger.info('Getting service categories cache status');

        // For now, we'll return basic cache information
        // In a production environment, you might want to implement proper cache status checking
        res.status(200).json({
            success: true,
            data: {
                cacheEnabled: true,
                cacheTTL: process.env.SERVICE_CATEGORY_CACHE_TTL_MINUTES || '30',
                lastUpdated: new Date().toISOString(),
            },
            timestamp: new Date().toISOString(),
        });
    } catch (error) {
        logger.error('Error getting service categories cache status:', error);

        res.status(500).json({
            success: false,
            error: 'Failed to get cache status',
            message: error instanceof Error ? error.message : 'Unknown error occurred',
            timestamp: new Date().toISOString(),
        });
    }
};
