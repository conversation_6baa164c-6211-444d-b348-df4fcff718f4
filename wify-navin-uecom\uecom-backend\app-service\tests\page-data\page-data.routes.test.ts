import request from 'supertest';
import express from 'express';
import pageDataRouter from '../../src/routes/page-data';
import { pageDataController } from '../../src/controllers/page-data';
import { pageData } from '../../src/data/pageData';

// Mock the page data controller
jest.mock('../../src/controllers/page-data');

describe('Page Data Routes', () => {
    let app: express.Application;

    beforeEach(() => {
        app = express();
        app.use(express.json());
        app.use('/api/page-data', pageDataRouter);
        
        // Reset all mocks
        jest.clearAllMocks();
    });

    describe('GET /api/page-data', () => {
        it('should respond to the GET method', async () => {
            // Mock the controller implementation
            (pageDataController.getAllPageData as jest.Mock).mockImplementation((_req, res) => {
                res.status(200).json(pageData);
            });
            
            // Make the request
            const response = await request(app).get('/api/page-data');
            
            // Verify the response
            expect(response.status).toBe(200);
            expect(response.body).toEqual(pageData);
        });

        it('should call the getAllPageData controller', async () => {
            // Mock the controller implementation
            (pageDataController.getAllPageData as jest.Mock).mockImplementation((_req, res) => {
                res.status(200).json(pageData);
            });
            
            // Make the request
            await request(app).get('/api/page-data');
            
            // Verify the controller was called
            expect(pageDataController.getAllPageData).toHaveBeenCalled();
        });
    });

    describe('GET /api/page-data/:pageName', () => {
        it('should respond to the GET method with a page name', async () => {
            // Mock the controller implementation
            (pageDataController.getPageData as jest.Mock).mockImplementation((_req, res) => {
                res.status(200).json(pageData);
            });
            
            // Make the request
            const response = await request(app).get('/api/page-data/home');
            
            // Verify the response
            expect(response.status).toBe(200);
            expect(response.body).toEqual(pageData);
        });

        it('should call the getPageData controller with the correct parameters', async () => {
            // Mock the controller implementation
            (pageDataController.getPageData as jest.Mock).mockImplementation((_req, res) => {
                res.status(200).json(pageData);
            });
            
            // Make the request
            await request(app).get('/api/page-data/home');
            
            // Verify the controller was called
            expect(pageDataController.getPageData).toHaveBeenCalled();
            
            // Verify the first argument (request) has the correct params
            const requestArg = (pageDataController.getPageData as jest.Mock).mock.calls[0][0];
            expect(requestArg.params.pageName).toBe('home');
        });

        it('should handle invalid paths', async () => {
            // Make the request to an invalid path
            const response = await request(app).get('/api/page-data/invalid/path');
            
            // Verify the response
            expect(response.status).toBe(404);
        });
    });
});
