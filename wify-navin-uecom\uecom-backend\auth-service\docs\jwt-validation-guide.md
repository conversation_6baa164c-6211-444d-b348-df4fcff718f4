# JWT Validation Guide

This guide explains how to validate JWT tokens and extract user information in protected routes.

## Table of Contents

1. [Introduction](#introduction)
2. [Session Cookie Authentication](#session-cookie-authentication)
3. [JWT Token Authentication](#jwt-token-authentication)
4. [Extracting User Information](#extracting-user-information)
5. [Middleware Usage Examples](#middleware-usage-examples)
6. [Best Practices](#best-practices)

## Introduction

The WIFY ECOM application uses JWT (JSON Web Tokens) for authentication. There are two main ways to authenticate:

1. **Session Cookie Authentication**: The preferred method for web applications
2. **JWT Token Authentication**: Used for API clients and mobile applications

## Session Cookie Authentication

Session cookies are secure HTTP-only cookies that contain a JWT token. This is the preferred method for web applications as it provides better security.

### How It Works

1. After successful OTP verification, a session token is generated and stored in a secure HTTP-only cookie
2. The cookie is automatically sent with every request to the server
3. The server validates the cookie and extracts the user ID

### Middleware

Use the `authenticateWithCookie` middleware to protect routes:

```typescript
import { authenticateWith<PERSON><PERSON>ie } from '../middlewares/cookie-auth.middleware';

// Protect a route with cookie authentication
router.get('/protected-route', authenticateWithCookie, (req, res) => {
  // Access the authenticated user
  const userId = req.user.id;
  
  // Your route logic here
  res.json({ message: 'Protected route accessed successfully', userId });
});
```

For routes that can work with or without authentication, use the `optionalAuthenticateWithCookie` middleware:

```typescript
import { optionalAuthenticateWithCookie } from '../middlewares/cookie-auth.middleware';

// Route that works with or without authentication
router.get('/optional-auth-route', optionalAuthenticateWithCookie, (req, res) => {
  // Check if user is authenticated
  if (req.user) {
    // User is authenticated
    const userId = req.user.id;
    res.json({ message: 'Authenticated user', userId });
  } else {
    // User is not authenticated
    res.json({ message: 'Anonymous user' });
  }
});
```

## JWT Token Authentication

JWT token authentication is used for API clients and mobile applications. The client sends the JWT token in the Authorization header.

### How It Works

1. After successful OTP verification, an access token and refresh token are returned to the client
2. The client stores these tokens (securely in local storage or memory)
3. The client sends the access token in the Authorization header with each request
4. The server validates the token and extracts the user ID

### Middleware

Use the `authenticate` middleware to protect routes:

```typescript
import { authenticate } from '../middlewares/auth.middleware';

// Protect a route with JWT authentication
router.get('/api/protected-resource', authenticate, (req, res) => {
  // Access the authenticated user
  const userId = req.user.id;
  
  // Your route logic here
  res.json({ message: 'Protected resource accessed successfully', userId });
});
```

## Extracting User Information

Once a user is authenticated, you can access their information through the `req.user` object:

```typescript
// In a protected route handler
const userId = req.user.id;

// You can then use this ID to fetch more user information from the database
const user = await databaseService.findUserById(userId);

// Or perform operations specific to this user
const userOrders = await orderService.getUserOrders(userId);
```

## Middleware Usage Examples

### Protecting an Entire Router

You can protect all routes in a router:

```typescript
const router = express.Router();

// Apply authentication to all routes in this router
router.use(authenticateWithCookie);

// All these routes are protected
router.get('/profile', profileController.getProfile);
router.put('/profile', profileController.updateProfile);
router.get('/orders', orderController.getUserOrders);
```

### Mixed Authentication

You can mix authentication methods in the same application:

```typescript
// Web routes with cookie authentication
router.get('/dashboard', authenticateWithCookie, dashboardController.getDashboard);

// API routes with JWT authentication
router.get('/api/user-data', authenticate, apiController.getUserData);
```

## Best Practices

1. **Always use HTTPS** in production to protect tokens and cookies
2. **Set appropriate token expiry times**:
   - Access tokens: Short-lived (1 hour)
   - Refresh tokens: Longer-lived (7 days)
   - Session tokens: Medium-lived (24 hours, configurable)
3. **Implement token refresh** for long-lived sessions
4. **Use secure, HTTP-only cookies** for web applications
5. **Implement CSRF protection** for cookie-based authentication
6. **Validate tokens properly** and handle errors gracefully
7. **Log authentication events** for security monitoring
8. **Implement rate limiting** to prevent brute force attacks
