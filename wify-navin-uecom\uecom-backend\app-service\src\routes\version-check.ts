import { Router } from 'express';
import { versionCheckController } from '../controllers/version-check';

const router = Router();

router.get('/', versionCheckController.getAll);
router.get('/platform/:platform', versionCheckController.getByPlatform);
router.get('/:id', versionCheckController.getById);
router.post('/', versionCheckController.create);
router.put('/:id', versionCheckController.update);
router.delete('/:id', versionCheckController.delete);

export default router;
