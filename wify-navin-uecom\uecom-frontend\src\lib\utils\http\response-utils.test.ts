import {
    handleClientError,
    handleServerError,
    handleNetworkError,
    handleError,
} from './response-utils'
import { AxiosError } from 'axios'

global.console.error = jest.fn()

class MockedCustomError extends Error {
    constructor(message: string) {
        super(message)
        this.name = this.constructor.name
    }
}

jest.mock('./response-utils', () => ({
    handleClientError: jest.fn(),
    handleServerError: jest.fn(),
    handleNetworkError: jest.fn(),
    handleError: jest.fn(),
}))

describe('Error Handling Tests', () => {
    let error: AxiosError

    beforeEach(() => {
        jest.clearAllMocks()
        error = {
            isAxiosError: true,
            message: 'Network Error',
            config: { url: 'http://test.com' },
            response: { status: 400, data: { message: 'Bad Request' } },
        } as AxiosError
    })

    test('should handle client error correctly', () => {
        error.response!.status = 400

        handleClientError(error)

        expect(console.error).toHaveBeenCalledWith(
            expect.stringContaining('Error encountered at http://test.com:'),
        )
        expect(console.error).toHaveBeenCalledWith(
            expect.objectContaining({
                name: 'ClientError',
                message: 'Bad Request',
            }),
        )
        expect(handleClientError).toHaveBeenCalledTimes(1)
    })

    test('should handle server error correctly', () => {
        error.response!.status = 500

        handleServerError(error)

        expect(console.error).toHaveBeenCalledWith(
            expect.stringContaining('Error encountered at http://test.com:'),
        )
        expect(console.error).toHaveBeenCalledWith(
            expect.objectContaining({
                name: 'ServerError',
                message: 'Server error occurred',
            }),
        )
        expect(handleServerError).toHaveBeenCalledTimes(1)
    })

    test('should handle network error correctly', () => {
        error.message = 'Network Timeout'

        handleNetworkError(error)

        expect(console.error).toHaveBeenCalledWith(
            expect.stringContaining('Error encountered at http://test.com:'),
        )
        expect(console.error).toHaveBeenCalledWith(
            expect.objectContaining({
                name: 'NetworkError',
                message: 'Network Timeout',
            }),
        )
        expect(handleNetworkError).toHaveBeenCalledTimes(1)
    })

    test('should categorize errors and call the correct handlers', () => {
        error.response = undefined // Network error won't have a response

        handleError(error, 'http://test.com')

        expect(handleNetworkError).toHaveBeenCalledWith(error)

        error.response = { status: 404, data: { message: 'Not Found' } } as any

        handleError(error, 'http://test.com')
        expect(handleClientError).toHaveBeenCalledWith(error)

        error.response = { status: 500, data: { message: 'Internal Server Error' } } as any

        handleError(error, 'http://test.com')
        expect(handleServerError).toHaveBeenCalledWith(error)

        const unknownError = new Error('Unknown Error')
        handleError(unknownError, 'http://test.com')
        expect(console.error).toHaveBeenCalledWith(
            expect.stringContaining('Unexpected error occurred when calling http://test.com'),
        )
    })
})
