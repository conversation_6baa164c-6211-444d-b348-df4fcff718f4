import '@testing-library/jest-dom'
import 'whatwg-fetch' // Ensures fetch API works in tests
import { TextEncoder, TextDecoder } from 'util'

// Polyfill `TextEncoder` and `TextDecoder` to avoid Next.js issues
global.TextEncoder = TextEncoder
// global.TextDecoder = TextDecoder;

// Polyfill `Request` from `whatwg-fetch`
global.Request = Request as unknown as typeof globalThis.Request

// Mock Next.js Router;
jest.mock('next/router', () => ({
    useRouter: () => ({
        push: jest.fn(),
        replace: jest.fn(),
        pathname: '/',
        query: {},
        asPath: '/',
    }),
}))
