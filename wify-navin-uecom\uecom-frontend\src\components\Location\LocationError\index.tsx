import React, { memo } from 'react'
import LocationIcon from '../LocationIcon'
import { LocationErrorProps } from '@/types/location'

/**
 * Component to display location-related errors in a user-friendly way
 * Enhanced with accessibility attributes and memoization
 */
const LocationError: React.FC<LocationErrorProps> = memo(({ message, onRetry }) => {
    // Map technical error messages to user-friendly messages
    const getUserFriendlyMessage = (errorMessage: string): string => {
        if (errorMessage.includes('permission') || errorMessage.includes('denied')) {
            return 'Location access was denied. Please enable location services in your browser settings to use this feature.'
        }

        if (errorMessage.includes('timeout')) {
            return "It's taking longer than expected to find your location. Please check your connection and try again."
        }

        if (errorMessage.includes('unavailable')) {
            return 'Location services are not available on your device or browser.'
        }

        if (errorMessage.includes('retrieve')) {
            return "We couldn't find your address based on your location. Please try again or enter your address manually."
        }

        // Default message for other errors
        return "We couldn't access your location. Please check your connection and location settings, then try again."
    }

    const friendlyMessage = getUserFriendlyMessage(message)

    // Provide alternative actions when location services are unavailable
    const isLocationUnavailable = message.includes('unavailable') || message.includes('denied')

    return (
        <div
            className="flex flex-col items-center justify-center min-h-screen bg-white p-4"
            role="alert"
            aria-live="assertive"
        >
            <div className="mb-6">
                <LocationIcon size="md" animate={false} />
            </div>

            <div className="text-center max-w-md bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
                <h2 className="text-lg font-semibold text-red-700 mb-3" tabIndex={0}>
                    Location Not Available
                </h2>

                <p className="text-gray-700 mb-4" tabIndex={0}>
                    {friendlyMessage}
                </p>

                {/* Show help instructions for common issues */}
                {isLocationUnavailable && (
                    <div className="mb-4 text-sm text-gray-600 bg-white p-3 rounded border border-gray-200">
                        <h3 className="font-medium mb-2">How to enable location:</h3>
                        <ul className="list-disc pl-5 text-left">
                            <li>Check that location services are enabled on your device</li>
                            <li>Make sure you've granted location permission to this website</li>
                            <li>Try using a different browser if the issue persists</li>
                        </ul>
                    </div>
                )}

                <div className="text-sm text-gray-500 mb-4">
                    <details className="cursor-pointer">
                        <summary className="mb-2">Technical details</summary>
                        <p className="p-2 bg-gray-100 rounded text-xs font-mono">{message}</p>
                    </details>
                </div>

                <div className="flex flex-col sm:flex-row justify-center gap-3">
                    {onRetry && (
                        <button
                            onClick={onRetry}
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-full transition-colors"
                            aria-label="Try fetching your location again"
                        >
                            Try Again
                        </button>
                    )}

                    {/* Add a manual entry option as fallback */}
                    <button
                        onClick={() =>
                            alert('This feature would allow manual address entry as a fallback')
                        }
                        className="bg-white border border-gray-300 hover:bg-gray-100 text-gray-700 font-medium py-2 px-4 rounded-full transition-colors"
                        aria-label="Enter your address manually"
                    >
                        Enter Address Manually
                    </button>
                </div>
            </div>
        </div>
    )
})

// Add display name for better debugging
LocationError.displayName = 'LocationError'

export default LocationError
