/**
 * Authentication Routes
 *
 * This module defines the routes for authentication operations,
 * including health checks, OTP requests, and OTP verification.
 *
 * @module routes/auth
 */

import express from 'express';
import {
    healthCheck,
    requestOtp,
    verifyOtp,
    refreshToken,
    logout,
} from '../controllers/auth';
import {
    validateRequestOtp,
    validateVerifyOtp,
} from '../middlewares/validation';
import { authenticate } from '../middlewares/auth.middleware';
import { authenticateWithCookie } from '../middlewares/cookie-auth.middleware';

const router = express.Router();

// Health check endpoint - no additional rate limiting
router.get('/', healthCheck);

// OTP request endpoint
router.post(
    '/request-otp',
    validateRequestOtp, // Input validation
    requestOtp // Controller
);

// OTP verification endpoint
router.post(
    '/verify-otp',
    validateVerifyOtp, // Input validation
    verifyOtp // Controller
);

// Token refresh endpoint
router.post(
    '/refresh-token',
    refreshToken // Controller
);

// Logout endpoint
router.post(
    '/logout',
    logout // Controller
);

// Protected route example - requires authentication with Bearer token
router.get(
    '/me',
    authenticate, // Authentication middleware
    (req, res) => {
        res.json({
            statusCode: 200,
            statusMessage: 'OK',
            message: 'Authentication successful (Bearer token)',
            data: {
                user: req.user,
            },
        });
    }
);

// Protected route example - requires authentication with cookie
router.get(
    '/me-cookie',
    authenticateWithCookie, // Cookie authentication middleware
    (req, res) => {
        res.json({
            statusCode: 200,
            statusMessage: 'OK',
            message: 'Authentication successful (Cookie)',
            data: {
                user: req.user,
            },
        });
    }
);

export default router;
