import { headers } from 'next/headers'
import { DeviceType } from '../constants/breakpoints'

/**
 * Interface for device detection results
 */
export interface DeviceInfo {
    isMobile: boolean
    isTablet: boolean
    isDesktop: boolean
    deviceType: DeviceType
}

/**
 * Detect device type from user agent string on the server
 *
 * @param userAgent - User agent string from request headers
 * @returns Object containing device type information
 */
export function detectDeviceFromUserAgent(userAgent: string): DeviceInfo {
    // Default to desktop view
    let isMobile = false
    let isTablet = false
    let isDesktop = true
    let deviceType = DeviceType.Desktop

    if (!userAgent) {
        return { isMobile, isTablet, isDesktop, deviceType }
    }

    // Mobile detection
    const mobileRegex = /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i
    const tabletRegex = /iPad|Tablet|PlayBook|Silk|Kindle|Nexus 7|Nexus 10/i

    // Check for tablet first (some tablets can also match mobile patterns)
    if (tabletRegex.test(userAgent)) {
        isMobile = false
        isTablet = true
        isDesktop = false
        deviceType = DeviceType.Tablet
    }
    // Then check for mobile
    else if (mobileRegex.test(userAgent)) {
        isMobile = true
        isTablet = false
        isDesktop = false
        deviceType = DeviceType.Mobile
    }

    return { isMobile, isTablet, isDesktop, deviceType }
}

/**
 * Get device type from request headers
 * This function should be used in Server Components
 *
 * @returns Promise resolving to object containing device type information
 */
export async function getDeviceFromHeaders(): Promise<DeviceInfo> {
    // Get headers in a server component - must be awaited in Next.js 14+
    const headersList = await headers()
    const userAgent = headersList.get('user-agent') || ''

    return detectDeviceFromUserAgent(userAgent)
}

/**
 * Get CSS class based on device type
 * Useful for conditional rendering in server components
 *
 * @param deviceType - The detected device type
 * @param mobileClass - CSS class to apply for mobile devices
 * @param tabletClass - CSS class to apply for tablet devices
 * @param desktopClass - CSS class to apply for desktop devices
 * @returns CSS class string
 */
export function getDeviceClass(
    deviceType: DeviceType,
    mobileClass: string = 'block md:hidden',
    tabletClass: string = 'hidden md:block lg:hidden',
    desktopClass: string = 'hidden lg:block',
): string {
    switch (deviceType) {
        case DeviceType.Mobile:
            return mobileClass
        case DeviceType.Tablet:
            return tabletClass
        case DeviceType.Desktop:
            return desktopClass
        default:
            return desktopClass
    }
}
