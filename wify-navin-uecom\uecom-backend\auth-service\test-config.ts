/**
 * Test Configuration
 *
 * This file is used to test the configuration service.
 */

import { ConfigService } from './src/configs/config.service';
import { logger } from './src/utils/logger';

// Create a new config service
const configService = new ConfigService();

// Log the configuration
logger.info('Configuration test');
logger.info(`Port: ${configService.getPort()}`);
logger.info(`Database URL: ${configService.getDatabaseUrl()}`);
logger.info(`JWT Secret: ${configService.getJwtSecret()}`);
logger.info(`Node Env: ${configService.getNodeEnv()}`);
logger.info(`MTalkz API Key: ${configService.getMtalkzApiKey()}`);
logger.info(`MTalkz Sender ID: ${configService.getMtalkzSenderId()}`);
logger.info(`Message Base URL: ${configService.getMessageBaseUrl()}`);
logger.info(`Message Sub Path: ${configService.getMessageSubPath()}`);
logger.info(`Allowed Origins: ${configService.getAllowedOrigins()}`);
logger.info(`Redis URL: ${configService.getRedisUrl()}`);
logger.info(`Redis OTP Prefix: ${configService.getRedisOtpPrefix()}`);
logger.info(
    `OTP Expiration Seconds: ${configService.getOtpExpirationSeconds()}`
);

logger.info('Configuration test completed');
