'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>Left, ArrowRight } from 'lucide-react'
import Image from 'next/image'

interface Screen {
    id: number
    title: string
    description: string
    icon: string
}

interface SplashScreenProps {
    screens: Screen[]
}

const SCREEN_DELAY_MS = 3000

export default function SplashScreen({ screens }: SplashScreenProps) {
    const router = useRouter()
    const [currentScreen, setCurrentScreen] = useState(0)
    const [direction, setDirection] = useState<'left' | 'right'>('right')
    const [imgError, setImgError] = useState(false)
    const [splashChecked, setSplashChecked] = useState(false)
    const [shouldShowSplash, setShouldShowSplash] = useState(true)

    // Helper function to check if user is logged in
    const isUserLoggedIn = useCallback(() => {
        return (
            localStorage.getItem('isVerified') === 'true' ||
            !!document.cookie.includes('session_token')
        )
    }, [])

    const redirectToAuth = useCallback(() => {
        router.replace('/auth')
    }, [router])

    const redirectToDiscovery = useCallback(() => {
        router.replace('/discovery')
    }, [router])

    // Check localStorage and authentication status only once at start
    useEffect(() => {
        const alreadySeen = localStorage.getItem('is_splash_show_done') === 'true'

        if (alreadySeen) {
            setShouldShowSplash(false)
            // If user is logged in, redirect to discovery page, otherwise to auth
            if (isUserLoggedIn()) {
                redirectToDiscovery()
            } else {
                redirectToAuth()
            }
        } else {
            setSplashChecked(true)
        }
    }, [redirectToAuth, redirectToDiscovery, isUserLoggedIn])

    // Auto-advance screen or redirect
    useEffect(() => {
        if (!shouldShowSplash || screens.length === 0) return

        const timer = setTimeout(() => {
            if (currentScreen < screens.length - 1) {
                setDirection('right')
                setCurrentScreen((prev) => prev + 1)
            } else {
                localStorage.setItem('is_splash_show_done', 'true')
                // Check if user is logged in before redirecting
                if (isUserLoggedIn()) {
                    redirectToDiscovery()
                } else {
                    redirectToAuth()
                }
            }
        }, SCREEN_DELAY_MS)

        return () => clearTimeout(timer)
    }, [
        currentScreen,
        screens.length,
        shouldShowSplash,
        redirectToAuth,
        redirectToDiscovery,
        isUserLoggedIn,
    ])

    const handleSkip = () => {
        localStorage.setItem('is_splash_show_done', 'true')
        // Check if user is logged in before redirecting
        if (isUserLoggedIn()) {
            redirectToDiscovery()
        } else {
            redirectToAuth()
        }
    }

    const nextScreen = () => {
        if (currentScreen < screens.length - 1) {
            setDirection('right')
            setCurrentScreen((prev) => prev + 1)
            setImgError(false)
        }
    }

    const prevScreen = () => {
        if (currentScreen > 0) {
            setDirection('left')
            setCurrentScreen((prev) => prev - 1)
            setImgError(false)
        }
    }

    const current = screens[currentScreen]

    // 💡 Don't render anything if splash was already shown
    if (!shouldShowSplash || !splashChecked) return null

    return (
        <div className="relative flex flex-col items-center justify-center h-screen bg-gradient-to-b from-white to-blue-100 px-4">
            <button
                onClick={handleSkip}
                className="absolute top-4 right-4 z-50 text-blue-500 text-lg sm:text-xl hover:text-blue-600 transition-colors"
                aria-label="Skip Onboarding"
            >
                Skip
            </button>

            <AnimatePresence mode="wait" custom={direction}>
                <div className="relative flex justify-center items-center w-full h-screen">
                    {currentScreen > 0 && (
                        <button
                            onClick={prevScreen}
                            className="absolute left-4 sm:left-6 text-blue-500 p-3 sm:p-4 rounded-full bg-white shadow-lg hover:bg-blue-50 z-50"
                            aria-label="Previous Screen"
                        >
                            <ArrowLeft size={24} />
                        </button>
                    )}

                    <motion.div
                        key={current.id}
                        custom={direction}
                        initial={{ opacity: 0, x: direction === 'right' ? 100 : -100 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: direction === 'right' ? -100 : 100 }}
                        transition={{ type: 'spring', stiffness: 100, damping: 20 }}
                        className="flex flex-col items-center space-y-6 px-4 sm:px-6 w-[90%] max-w-md sm:max-w-lg mx-auto"
                    >
                        <div className="p-4 bg-white rounded-full shadow-lg w-28 h-28 sm:w-32 sm:h-32 flex items-center justify-center">
                            <Image
                                src={imgError ? '/icons/placeholder.png' : current.icon}
                                alt={current.title}
                                width={128}
                                height={128}
                                className="w-full h-full object-contain"
                                onError={() => setImgError(true)}
                                priority
                            />
                        </div>

                        <h1 className="text-xl sm:text-2xl font-bold text-blue-900 text-center">
                            {current.title}
                        </h1>
                        <p className="text-center text-blue-700 max-w-xs sm:max-w-sm px-2">
                            {current.description}
                        </p>
                    </motion.div>

                    {currentScreen < screens.length - 1 && (
                        <button
                            onClick={nextScreen}
                            className="absolute right-4 sm:right-6 text-blue-500 p-3 sm:p-4 rounded-full bg-white shadow-lg hover:bg-blue-50 z-50"
                            aria-label="Next Screen"
                        >
                            <ArrowRight size={24} />
                        </button>
                    )}
                </div>
            </AnimatePresence>
        </div>
    )
}
