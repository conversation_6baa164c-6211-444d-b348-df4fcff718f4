import { ILayoutDataSource, ILayoutService, layoutService } from './LayoutService'
import { PageData, SectionData } from '../types'

// Mock data for testing
const mockPageData: PageData = {
    sections: [
        {
            type: 'header',
            label: 'Header',
            value: {
                location: 'Test Location',
                Image: 'test-image.png',
                logoText: 'Test Logo',
                navLinks: [],
                businessLink: 'Business',
            },
        },
        {
            type: 'content',
            label: 'Content',
            value: {
                title: 'Test Content',
            },
        },
        {
            type: 'footer',
            label: 'Footer',
            value: {
                tagline: 'Test Tagline',
                address: 'Test Address',
                phone: 'Test Phone',
                email: '<EMAIL>',
                legalTitle: 'Legal',
                companyTitle: 'Company',
                connectTitle: 'Connect',
                legalLinks: [],
                companyLinks: [],
                socials: [],
                copyright: 'Copyright',
                companyName: 'Test Company',
            },
        },
    ],
}

// Mock data source for testing
class TestLayoutDataSource implements ILayoutDataSource {
    private mockData: PageData
    private shouldFail: boolean

    constructor(mockData: PageData, shouldFail: boolean = false) {
        this.mockData = mockData
        this.shouldFail = shouldFail
    }

    async fetchPageData(pageName: string): Promise<PageData> {
        if (this.shouldFail) {
            throw new Error('Failed to fetch page data')
        }
        return this.mockData
    }
}

describe('LayoutService', () => {
    let service: ILayoutService
    let mockDataSource: ILayoutDataSource

    beforeEach(() => {
        // Create a new mock data source and service for each test
        mockDataSource = new TestLayoutDataSource(mockPageData)
        service = new (layoutService.constructor as any)(mockDataSource)
    })

    describe('getPageData', () => {
        it('should return page data from the data source', async () => {
            const result = await service.getPageData('home')
            expect(result).toEqual(mockPageData)
        })

        it('should return empty sections array when data source fails', async () => {
            const failingDataSource = new TestLayoutDataSource(mockPageData, true)
            const failingService = new (layoutService.constructor as any)(failingDataSource)

            const result = await failingService.getPageData('home')
            expect(result).toEqual({ sections: [] })
        })

        it('should return empty sections array when data is invalid', async () => {
            const invalidDataSource = new TestLayoutDataSource({ sections: null as any })
            const invalidService = new (layoutService.constructor as any)(invalidDataSource)

            const result = await invalidService.getPageData('home')
            expect(result).toEqual({ sections: [] })
        })
    })

    describe('getSectionsByType', () => {
        it('should return sections of the specified type', () => {
            const result = service.getSectionsByType(mockPageData.sections, 'header')
            expect(result).toHaveLength(1)
            expect(result[0].type).toBe('header')
        })

        it('should return empty array when no sections match the type', () => {
            const result = service.getSectionsByType(mockPageData.sections, 'unknown')
            expect(result).toHaveLength(0)
        })
    })

    describe('getHeaderSection', () => {
        it('should return the header section', () => {
            const result = service.getHeaderSection(mockPageData.sections)
            expect(result).toBeDefined()
            expect(result?.type).toBe('header')
        })

        it('should return undefined when no header section exists', () => {
            const sectionsWithoutHeader = mockPageData.sections.filter((s) => s.type !== 'header')
            const result = service.getHeaderSection(sectionsWithoutHeader)
            expect(result).toBeUndefined()
        })
    })

    describe('getFooterSection', () => {
        it('should return the footer section', () => {
            const result = service.getFooterSection(mockPageData.sections)
            expect(result).toBeDefined()
            expect(result?.type).toBe('footer')
        })

        it('should return undefined when no footer section exists', () => {
            const sectionsWithoutFooter = mockPageData.sections.filter((s) => s.type !== 'footer')
            const result = service.getFooterSection(sectionsWithoutFooter)
            expect(result).toBeUndefined()
        })
    })
})
