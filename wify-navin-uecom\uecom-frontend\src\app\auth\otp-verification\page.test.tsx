import { render, screen, waitFor } from '@testing-library/react'
import OTPVerification from './page'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/app/stores/auth/auth-store'

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
    useRouter: jest.fn(),
}))

// Mock the auth store
jest.mock('@/app/stores/auth/auth-store', () => ({
    useAuthStore: jest.fn(),
}))

describe('OTP Verification Page', () => {
    let mockRouterPush: jest.Mock
    let mockClearError: jest.Mock
    let mockVerifyOtp: jest.Mock
    let mockResendOtp: jest.Mock

    beforeEach(() => {
        // Setup router mock
        mockRouterPush = jest.fn()
        ;(useRouter as jest.Mock).mockReturnValue({ push: mockRouterPush })

        // Setup auth store mocks
        mockClearError = jest.fn()
        mockVerifyOtp = jest.fn()
        mockResendOtp = jest.fn()

        // Clear localStorage
        localStorage.clear()
    })

    afterEach(() => {
        jest.clearAllMocks()
    })

    it('should redirect to home page if mobile number is not present', async () => {
        // Mock the auth store with empty mobile
        ;(useAuthStore as jest.Mock).mockReturnValue({
            mobile: '',
            isLoading: false,
            error: null,
            verifyOtp: mockVerifyOtp,
            resendOtp: mockResendOtp,
            clearError: mockClearError,
        })

        render(<OTPVerification />)

        // Check if router.push was called with '/'
        await waitFor(() => {
            expect(mockRouterPush).toHaveBeenCalledWith('/')
        })
    })

    it('should not redirect if mobile number is present', () => {
        // Mock the auth store with a mobile number
        ;(useAuthStore as jest.Mock).mockReturnValue({
            mobile: '9876543210',
            isLoading: false,
            error: null,
            verifyOtp: mockVerifyOtp,
            resendOtp: mockResendOtp,
            clearError: mockClearError,
        })

        render(<OTPVerification />)

        // Check if the page renders correctly
        expect(screen.getByText('Verify Your Phone Number')).toBeInTheDocument()
        expect(screen.getByText('9876543210')).toBeInTheDocument()

        // Router.push should not be called for the empty mobile case
        expect(mockRouterPush).not.toHaveBeenCalledWith('/')
    })

    it('should redirect if user is already verified', async () => {
        // Set localStorage to indicate user is verified
        localStorage.setItem('isVerified', 'true')

        // Mock the auth store with a mobile number
        ;(useAuthStore as jest.Mock).mockReturnValue({
            mobile: '9876543210',
            isLoading: false,
            error: null,
            verifyOtp: mockVerifyOtp,
            resendOtp: mockResendOtp,
            clearError: mockClearError,
        })

        render(<OTPVerification />)

        // Check if router.push was called with '/'
        await waitFor(() => {
            expect(mockRouterPush).toHaveBeenCalledWith('/')
        })
    })
})
