/**
 * Page Data Routes
 * 
 * This file defines the routes for the page data API.
 */

import { Router } from 'express';
import { pageDataController } from '../controllers/page-data';

const router = Router();

// GET /api/page-data
// Returns all available page data
router.get('/', pageDataController.getAllPageData);

// GET /api/page-data/:pageName
// Returns page data for the specified page
router.get('/:pageName', pageDataController.getPageData);

export default router;
