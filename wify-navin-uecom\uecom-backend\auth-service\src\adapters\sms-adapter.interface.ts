/**
 * SMS Adapter Interface
 *
 * This interface defines the contract for any SMS service adapter implementation.
 * It follows the Adapter pattern to provide a common interface for different SMS providers.
 *
 * @module adapters/sms-adapter
 */

import { SmsRequest } from '../types/auth.types';
import { APIResponse } from '../types/response.types';

export interface ISmsAdapter {
    /**
     * Sends an SMS using the configured SMS gateway
     *
     * @param {SmsRequest} request - Object containing recipient phone number and message text
     * @returns {Promise<APIResponse>} A structured API response indicating success or failure
     */
    sendSMS(request: SmsRequest): Promise<APIResponse>;
}
