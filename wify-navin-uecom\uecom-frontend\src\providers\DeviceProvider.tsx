'use client'

import React, { createContext, useContext, ReactNode } from 'react'
import { useDevice, DeviceInfo } from '../hooks/useDevice'
import { DeviceType } from '../lib/constants/breakpoints'

// Create context with default values
const DeviceContext = createContext<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    deviceType: DeviceType.Desktop,
    width: 0,
    height: 0,
    isMounted: false,
    orientation: 'portrait', // or 'landscape', depending on your default logic
    hasPointer: true, // assuming desktop as default
})

// Custom hook for using device context
export const useDeviceContext = () => useContext(DeviceContext)

// Device provider props
interface DeviceProviderProps {
    children: ReactNode
    initialDeviceType?: DeviceType
}

/**
 * Device provider component that provides device information to all children
 * Uses the useDevice hook and provides the information through context
 *
 * @param children - Child components
 * @param initialDeviceType - Optional initial device type for SSR
 *
 * @example
 * ```tsx
 * <DeviceProvider initialDeviceType={DeviceType.Desktop}>
 *   <App />
 * </DeviceProvider>
 * ```
 */
export function DeviceProvider({
    children,
    initialDeviceType = DeviceType.Desktop,
}: DeviceProviderProps) {
    // Use our custom hook to get device information
    const deviceInfo = useDevice(initialDeviceType)

    return <DeviceContext.Provider value={deviceInfo}>{children}</DeviceContext.Provider>
}
