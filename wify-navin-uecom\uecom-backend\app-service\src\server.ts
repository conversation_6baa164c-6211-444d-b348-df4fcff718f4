/**
 * Main Application Service Entry Point
 *
 * This file initializes and configures the Express server for the App Service,
 * which serves as the main backend service for the WIFY ECOM application.
 * It follows clean architecture principles with proper separation of concerns.
 *
 * @module server
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { configService } from './config/config.service';
import appRoutes from './routes/app';
import versionCheckRoutes from './routes/version-check';
import pageDataRoutes from './routes/page-data';
import brandsRoutes from './routes/brands';
import serviceCategoriesRoutes from './routes/serviceCategories';
import { requestLogger } from './middlewares/logger';
import { logger } from './utils/logger';

// Load environment variables from .env file
dotenv.config();

// Initialize Express application
const app = express();

// Apply middleware
app.use(
    cors({
        origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization'],
        credentials: true,
    })
); // Enable Cross-Origin Resource Sharing with proper configuration

// Parse JSON request bodies with size limit to prevent DoS attacks
app.use(express.json({ limit: '100kb' }));

// Log all incoming requests
app.use(requestLogger);

// Register route handlers
app.use('/api', appRoutes); // General application routes
app.use('/api/version-check', versionCheckRoutes); // Version check routes
app.use('/api/page-data', pageDataRoutes); // Page data routes
app.use('/api/brands', brandsRoutes); // Brands routes
app.use('/api/service-categories', serviceCategoriesRoutes); // Service categories routes

// Health check endpoint
app.get('/health', (_req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'app-service',
    });
});

// Root endpoint redirect to health check
app.get('/', (_req, res) => {
    res.redirect('/health');
});

// Add global error handler
app.use(
    (
        err: any,
        _req: express.Request,
        res: express.Response,
        _next: express.NextFunction
    ) => {
        logger.error('Unhandled error:', err);

        // Don't expose error details in production
        const message =
            process.env.NODE_ENV === 'production'
                ? 'Internal Server Error'
                : err.message || 'Something went wrong';

        res.status(500).json({
            statusCode: 500,
            statusMessage: 'Internal Server Error',
            message,
            error:
                process.env.NODE_ENV === 'production' ? undefined : err.stack,
        });
    }
);

// Get port from configuration
const PORT = configService.getPort();

// Start the server
app.listen(PORT, () => {
    logger.info(`🚀 App service running at http://localhost:${PORT}`);
    logger.info(`🔍 Health check available at http://localhost:${PORT}/health`);
    logger.info(`🧩 SOLID principles and clean architecture implemented`);
});
