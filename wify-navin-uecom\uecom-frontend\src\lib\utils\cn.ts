import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

/**
 * Utility function to merge class names with Tailwind CSS classes
 * Uses clsx for conditional class names and tailwind-merge to handle conflicting Tailwind classes
 *
 * @example
 * ```tsx
 * <div className={cn('text-red-500', isActive && 'bg-blue-500', 'p-4')}>
 *   Content
 * </div>
 * ```
 */
export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs))
}
