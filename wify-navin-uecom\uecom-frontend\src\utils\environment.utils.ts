/**
 * Environment Utilities
 *
 * This module provides utility functions for environment-specific behavior.
 * It centralizes environment checks to ensure consistent behavior across the application.
 */

/**
 * Environment types
 */
export enum Environment {
    DEVELOPMENT = 'development',
    TEST = 'test',
    STAGING = 'staging',
    PRODUCTION = 'production',
}

/**
 * Static OTP for non-production environments
 */
export const STATIC_OTP = '999999'

/**
 * Environment utility class
 */
export class EnvironmentUtils {
    /**
     * Checks if the current environment is production
     *
     * @returns {boolean} True if the current environment is production
     */
    static isProduction(): boolean {
        const nodeEnv = process.env.NODE_ENV
        return nodeEnv === Environment.PRODUCTION
    }

    /**
     * Checks if the current environment is development
     *
     * @returns {boolean} True if the current environment is development
     */
    static isDevelopment(): boolean {
        const nodeEnv = process.env.NODE_ENV
        return nodeEnv === Environment.DEVELOPMENT
    }

    /**
     * Checks if the current environment is test
     *
     * @returns {boolean} True if the current environment is test
     */
    static isTest(): boolean {
        const nodeEnv = process.env.NODE_ENV
        return nodeEnv === Environment.TEST
    }

    /**
     * Checks if the current environment is staging
     *
     * @returns {boolean} True if the current environment is staging
     */
    static isStaging(): boolean {
        const nodeEnv = process.env.NODE_ENV as Environment
        return nodeEnv === Environment.STAGING
    }

    /**
     * Gets the current environment
     *
     * @returns {string} The current environment
     */
    static getEnvironment(): string {
        return process.env.NODE_ENV || 'development'
    }

    /**
     * Checks if static OTP should be used
     *
     * @returns {boolean} True if static OTP should be used
     */
    static shouldUseStaticOtp(): boolean {
        return !this.isProduction()
    }
}
