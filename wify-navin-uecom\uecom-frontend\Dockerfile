# Use official Node.js LTS image as base
FROM public.ecr.aws/docker/library/node:23.8.0 AS builder

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json  ./

# Install dependencies
RUN yarn

# Copy all source files
COPY . .

# Build the Next.js app
RUN yarn run build

# Production image
FROM public.ecr.aws/docker/library/node:23.8.0-alpine AS runner

WORKDIR /app

# Copy only necessary files from builder stage
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/public ./public
COPY --from=builder /app/next.config.js ./


ENV NEXT_PUBLIC_APP_SERVICE_URL=http://localhost:8000
ENV NEXT_PUBLIC_AUTH_SERVICE_URL=http://localhost:4000
ENV NEXT_PUBLIC_ECOM_AUTH_SERVICE_URL=http://localhost:4000/v1/auth
ENV NEXT_PUBLIC_BASE_URL=http://localhost:3000
ENV NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# For server-side API calls (not exposed to the client)
ENV APP_SERVICE_URL=http://localhost:8000
ENV AUTH_SERVICE_URL=http://localhost:4000

 # Server-side only API key (not exposed to client)
ENV GOOGLE_MAPS_API_KEY=AIzaSyAJaPgUY5jaMnpSPRn3Cn0q9_d-y_Uhwjg


# Expose port 3000
EXPOSE 3000

# Start the Next.js server in production mode
CMD ["yarn", "start-direct"]
