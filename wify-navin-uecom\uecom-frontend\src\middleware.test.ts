import { middleware } from '@/middleware'
import { NextRequest, NextResponse } from 'next/server'

jest.mock('next/server', () => ({
    NextResponse: {
        next: jest.fn(() => ({
            type: 'next-response',
        })),
        redirect: jest.fn((url) => ({
            type: 'redirect',
            url,
        })),
    },
}))

describe('Middleware', () => {
    const mockRequest = (): NextRequest =>
        ({
            nextUrl: { pathname: '/' },
            headers: new Headers(),
        }) as unknown as NextRequest

    it('should return a NextResponse instance', () => {
        const req = mockRequest()
        const response = middleware(req)
        expect(response).toEqual(NextResponse.next())
    })

    it('should always call NextResponse.next()', () => {
        const req = mockRequest()
        middleware(req)
        expect(NextResponse.next).toHaveBeenCalled()
    })

    it('should not modify the request', () => {
        const req = mockRequest()
        const response = middleware(req)
        expect(req).toEqual(mockRequest()) // Request remains unchanged
        expect(response).toEqual(NextResponse.next())
    })

    it('should work with different request URLs', () => {
        const urls = ['/dashboard', '/auth', '/api/data']
        urls.forEach((url) => {
            const req = { nextUrl: { pathname: url } } as NextRequest
            const response = middleware(req)
            expect(response).toEqual(NextResponse.next())
        })
    })

    it('should handle unexpected errors gracefully', () => {
        jest.spyOn(NextResponse, 'next').mockImplementation(() => {
            throw new Error('Unexpected failure')
        })

        const req = mockRequest()
        expect(() => middleware(req)).toThrow('Unexpected failure')
    })
})
