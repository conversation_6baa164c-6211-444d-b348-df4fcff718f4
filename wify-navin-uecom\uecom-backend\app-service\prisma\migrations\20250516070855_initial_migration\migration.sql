-- CreateTable
CREATE TABLE "cl_tx_consumer" (
    "consumer_id" UUID NOT NULL,
    "name" VARCHAR(500),
    "email" VARCHAR(50),
    "mobile_no" VARCHAR(10) NOT NULL,
    "otp" INTEGER,
    "otp_exp" TIMESTAMP(3),
    "profile_image" VARCHAR(500),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "org_id" UUID,
    "c_ip" VARCHAR(100),
    "c_user_agent" VARCHAR(500),
    "c_time" TIMESTAMP(3),
    "u_ip" VARCHAR(100),
    "u_user_agent" VARCHAR(500),
    "u_time" TIMESTAMP(3),

    CONSTRAINT "cl_tx_consumer_pkey" PRIMARY KEY ("consumer_id")
);

-- CreateTable
CREATE TABLE "cl_tx_usr_logins" (
    "userId" UUID NOT NULL,
    "loginNo" SERIAL NOT NULL,
    "indType" VARCHAR(100) NOT NULL,
    "userType" VARCHAR(20),
    "hash" VARCHAR(100),
    "lastSeen" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "indId" VARCHAR(100),
    "c_ip" VARCHAR(100),
    "c_user_agent" VARCHAR(500),
    "c_time" TIMESTAMP(3),
    "u_ip" VARCHAR(100),
    "u_user_agent" VARCHAR(500),
    "u_time" TIMESTAMP(3),

    CONSTRAINT "cl_tx_usr_logins_pkey" PRIMARY KEY ("userId","loginNo")
);

-- CreateTable
CREATE TABLE "cl_tx_usr_identities" (
    "dbId" UUID NOT NULL,
    "usrId" UUID,
    "identityType" UUID,
    "id" TEXT,
    "key" TEXT,
    "token" TEXT,
    "orgId" UUID,
    "c_ip" VARCHAR(100),
    "c_user_agent" VARCHAR(500),
    "c_time" TIMESTAMP(3),
    "u_ip" VARCHAR(100),
    "u_user_agent" VARCHAR(500),
    "u_time" TIMESTAMP(3),

    CONSTRAINT "cl_tx_usr_identities_pkey" PRIMARY KEY ("dbId")
);

-- CreateTable
CREATE TABLE "sys_cf_identity" (
    "identityId" UUID NOT NULL,
    "type" VARCHAR(20),

    CONSTRAINT "sys_cf_identity_pkey" PRIMARY KEY ("identityId")
);

-- CreateIndex
CREATE UNIQUE INDEX "cl_tx_consumer_mobile_no_key" ON "cl_tx_consumer"("mobile_no");

-- CreateIndex
CREATE INDEX "cl_tx_usr_identities_usrId_idx" ON "cl_tx_usr_identities"("usrId");

-- AddForeignKey
ALTER TABLE "cl_tx_usr_identities" ADD CONSTRAINT "cl_tx_usr_identities_identityType_fkey" FOREIGN KEY ("identityType") REFERENCES "sys_cf_identity"("identityId") ON DELETE SET NULL ON UPDATE CASCADE;
