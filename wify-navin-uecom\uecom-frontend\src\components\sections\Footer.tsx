import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { MapPin, Mail, Phone, CopyrightIcon } from 'lucide-react'
import { FooterSection } from '../../types'
import { useDeviceContext } from '../../providers/DeviceProvider'

// Custom social media icon components
const InstagramIcon = ({ size = 24, ...props }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        {...props}
    >
        <rect x="2" y="2" width="20" height="20" rx="5" ry="5" />
        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
        <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" />
    </svg>
)

const LinkedinIcon = ({ size = 24, ...props }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        {...props}
    >
        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
        <rect x="2" y="9" width="4" height="12" />
        <circle cx="4" cy="4" r="2" />
    </svg>
)

const FacebookIcon = ({ size = 24, ...props }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        {...props}
    >
        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" />
    </svg>
)

interface FooterProps {
    data: FooterSection | null
}

const Footer: React.FC<FooterProps> = ({ data }) => {
    const year = new Date().getFullYear()
    const router = useRouter()

    // Default values for when data is null
    const defaultValue = {
        tagline: 'We Install For You',
        address: 'D-704, Remi Bizcourt, Off Veera Desai Road, Andheri, West Mumbai 400053',
        phone: '+****************',
        email: '<EMAIL>',
        legalTitle: 'Legal',
        companyTitle: 'Company',
        connectTitle: 'Connect With Us',
        legalLinks: [
            { label: 'Terms of Service', url: '/terms' },
            { label: 'Privacy Policy', url: '/privacy' },
        ],
        companyLinks: [
            { label: 'About Us', url: '/about' },
            { label: 'Contact', url: '/contact' },
        ],
        socials: [
            { label: 'Instagram', url: 'https://instagram.com', icon: 'instagram' },
            { label: 'LinkedIn', url: 'https://linkedin.com', icon: 'linkedin' },
            { label: 'Facebook', url: 'https://facebook.com', icon: 'facebook' },
        ],
        copyright: 'All rights reserved',
        companyName: 'Wify',
    }

    // Use provided data or default data
    const footerData = data?.value || defaultValue

    const {
        tagline,
        address,
        phone,
        email,
        legalTitle,
        companyTitle,
        connectTitle,
        legalLinks,
        companyLinks,
        socials,
        copyright,
        companyName,
    } = footerData
    const { isMobile, isTablet, isMounted } = useDeviceContext()

    const MobileView = () => (
        <footer className="bg-blue-primary font-poppins block sm:block md:hidden">
            <div className="text-sm pb-16 flex flex-col gap-10">
                <div className="flex justify-start items-start flex-col gap-4 p-4">
                    <h1 className="text-white py-4 text-base">{tagline}</h1>
                    <div className="flex gap-2 w-full justify-start items-start">
                        <div className="flex justify-start items-start">
                            <MapPin className="text-base text-gray-300 mt-1" size={16} />
                        </div>
                        <div className="text-gray-300 leading-6 text-xs font-light tracking-2percent">
                            {address}
                        </div>
                    </div>
                    <div className="flex gap-2 items-center w-full justify-start">
                        <div className="flex justify-start items-start">
                            <Phone className="text-base text-gray-300" size={16} />
                        </div>
                        <div className="text-gray-300 text-xs font-light">{phone}</div>
                    </div>
                    <div className="flex gap-2 items-center w-full justify-start">
                        <div className="flex justify-start items-start">
                            <Mail className="text-base text-gray-300" size={16} />
                        </div>
                        <div className="text-gray-300 text-xs font-light">{email}</div>
                    </div>
                </div>
                <div className="w-full flex justify-center items-start">
                    <div className="flex justify-start px-5 w-1/2 flex-col">
                        <div>
                            <h2 className="text-white flex justify-start items-start w-full text-base pb-2">
                                {legalTitle}
                            </h2>
                            <ul className="text-gray-300 text-xs font-light flex items-start flex-col">
                                {legalLinks &&
                                    legalLinks.map((link) => (
                                        <Link
                                            key={link.label}
                                            href={link.url}
                                            className="py-1.5 cursor-pointer hover:text-white"
                                        >
                                            {link.label}
                                        </Link>
                                    ))}
                            </ul>
                        </div>
                    </div>
                    <div className="flex w-1/2 items-center flex-col">
                        <div>
                            <h2 className="text-white text-base pb-2">{companyTitle}</h2>
                            <ul className="text-gray-300 text-xs font-light flex justify-start items-start flex-col">
                                {companyLinks &&
                                    companyLinks.map((link) => (
                                        <Link
                                            key={link.label}
                                            href={link.url}
                                            className="py-1.5 cursor-pointer hover:text-white"
                                        >
                                            {link.label}
                                        </Link>
                                    ))}
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col items-start px-5">
                    <div>
                        <p className="text-white text-sm">{connectTitle}</p>
                        <div className="flex items-center py-4 gap-3">
                            {socials &&
                                socials.map((social) => (
                                    <button
                                        key={social.label}
                                        onClick={
                                            typeof window !== 'undefined'
                                                ? () => router.push(social.url)
                                                : undefined
                                        }
                                        className="text-gray-300 hover:text-white"
                                    >
                                        {social.icon === 'instagram' && <InstagramIcon size={24} />}
                                        {social.icon === 'linkedin' && <LinkedinIcon size={24} />}
                                        {social.icon === 'facebook' && <FacebookIcon size={24} />}
                                    </button>
                                ))}
                        </div>
                    </div>
                </div>
                <div className="text-center text-gray-300 text-xs border-t border-brand-gray-selection font-light py-4 flex items-center justify-center gap-1">
                    {copyright}
                    <CopyrightIcon size={12} className="inline-block" />
                    {year} – {companyName}
                </div>
            </div>
        </footer>
    )

    const TabletView = () => (
        <footer className="bg-blue-primary font-poppins hidden md:block lg:hidden">
            <div className="text-sm pb-12 flex flex-col gap-8 px-6">
                <div className="flex justify-between items-start pt-6">
                    <div className="flex flex-col gap-4 w-1/2">
                        <h1 className="text-white py-3 text-lg">{tagline}</h1>
                        <div className="flex gap-2 w-full justify-start items-start">
                            <div className="flex justify-start items-start">
                                <MapPin className="text-base text-gray-300 mt-1" size={18} />
                            </div>
                            <div className="text-gray-300 leading-6 text-sm font-light tracking-2percent">
                                {address}
                            </div>
                        </div>
                        <div className="flex gap-2 items-center w-full justify-start">
                            <div className="flex justify-start items-start">
                                <Phone className="text-base text-gray-300" size={18} />
                            </div>
                            <div className="text-gray-300 text-sm font-light">{phone}</div>
                        </div>
                        <div className="flex gap-2 items-center w-full justify-start">
                            <div className="flex justify-start items-start">
                                <Mail className="text-base text-gray-300" size={18} />
                            </div>
                            <div className="text-gray-300 text-sm font-light">{email}</div>
                        </div>
                    </div>

                    <div className="flex w-1/2 justify-between">
                        <div className="flex flex-col">
                            <h2 className="text-white text-base py-3">{legalTitle}</h2>
                            <ul className="text-gray-300 text-sm font-light flex flex-col gap-2">
                                {legalLinks &&
                                    legalLinks.map((link) => (
                                        <Link
                                            key={link.label}
                                            href={link.url}
                                            className="py-1 cursor-pointer hover:text-white"
                                        >
                                            {link.label}
                                        </Link>
                                    ))}
                            </ul>
                        </div>

                        <div className="flex flex-col">
                            <h2 className="text-white text-base py-3">{companyTitle}</h2>
                            <ul className="text-gray-300 text-sm font-light flex flex-col gap-2">
                                {companyLinks &&
                                    companyLinks.map((link) => (
                                        <Link
                                            key={link.label}
                                            href={link.url}
                                            className="py-1 cursor-pointer hover:text-white"
                                        >
                                            {link.label}
                                        </Link>
                                    ))}
                            </ul>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col items-start">
                    <p className="text-white text-base py-2">{connectTitle}</p>
                    <div className="flex items-center py-2 gap-3">
                        {socials &&
                            socials.map((social) => (
                                <button
                                    key={social.label}
                                    onClick={
                                        typeof window !== 'undefined'
                                            ? () => router.push(social.url)
                                            : undefined
                                    }
                                    className="text-gray-300 hover:text-white"
                                >
                                    {social.icon === 'instagram' && <InstagramIcon size={26} />}
                                    {social.icon === 'linkedin' && <LinkedinIcon size={26} />}
                                    {social.icon === 'facebook' && <FacebookIcon size={26} />}
                                </button>
                            ))}
                    </div>
                </div>

                <div className="text-center text-gray-300 text-sm border-t border-brand-gray-selection font-light py-4 flex items-center justify-center gap-1">
                    {copyright} <CopyrightIcon size={14} /> {year} – {companyName}
                </div>
            </div>
        </footer>
    )

    const DesktopView = () => (
        <footer className="bg-blue-primary font-poppins hidden lg:block">
            <div className="w-full h-full text-white md:px-24 lg:px-30">
                <div className="py-6 flex justify-between gap-4 w-full h-full">
                    <div className="flex w-[30%] justify-start items-start flex-col gap-3">
                        <h1 className="text-white text-start tracking-2percent text-lg py-6">
                            {tagline}
                        </h1>
                        <div className="flex gap-2 w-full justify-start">
                            <div className="flex justify-start items-start mt-1">
                                <MapPin className="text-base" size={14} />
                            </div>
                            <div className="text-gray-300 flex flex-col gap-4 font-light text-sm">
                                <p className="tracking-2percent leading-6">{address}</p>
                            </div>
                        </div>
                        <div className="flex gap-2 items-center w-full justify-start">
                            <div className="flex justify-start items-start">
                                <Phone className="text-sm" size={14} />
                            </div>
                            <div className="text-gray-300 font-light text-sm">{phone}</div>
                        </div>
                        <div className="flex gap-2 items-center w-full justify-start">
                            <div className="flex justify-start items-start">
                                <Mail className="text-base" size={14} />
                            </div>
                            <div className="text-gray-300 font-light text-sm">{email}</div>
                        </div>
                    </div>

                    <div className="flex justify-end items-start">
                        <div className="flex flex-col justify-start items-start gap-2">
                            <h2 className="text-white text-lg py-6">{legalTitle}</h2>
                            <ul className="text-gray-300 font-light flex items-start flex-col gap-3">
                                {legalLinks &&
                                    legalLinks.map((link) => (
                                        <Link
                                            key={link.label}
                                            href={link.url}
                                            className="py-1 cursor-pointer hover:text-white text-sm font-light"
                                        >
                                            {link.label}
                                        </Link>
                                    ))}
                            </ul>
                        </div>
                    </div>

                    <div className="flex justify-end items-center">
                        <div className="flex flex-col justify-start items-start gap-2">
                            <h2 className="text-white text-lg py-6">{companyTitle}</h2>
                            <ul className="text-gray-300 font-light flex items-start flex-col gap-3">
                                {companyLinks &&
                                    companyLinks.map((link) => (
                                        <Link
                                            key={link.label}
                                            href={link.url}
                                            className="py-1 hover:text-white text-sm"
                                        >
                                            {link.label}
                                        </Link>
                                    ))}
                            </ul>
                        </div>
                    </div>

                    <div className="flex justify-end items-start">
                        <div className="flex flex-col justify-center items-start gap-2">
                            <p className="py-6 text-lg">{connectTitle}</p>
                            <div className="flex items-center gap-2 py-1">
                                {socials &&
                                    socials.map((social) => (
                                        <button
                                            key={social.label}
                                            onClick={
                                                typeof window !== 'undefined'
                                                    ? () => router.push(social.url)
                                                    : undefined
                                            }
                                            className="text-gray-300 hover:text-white"
                                        >
                                            {social.icon === 'instagram' && (
                                                <InstagramIcon size={30} />
                                            )}
                                            {social.icon === 'linkedin' && (
                                                <LinkedinIcon size={30} />
                                            )}
                                            {social.icon === 'facebook' && (
                                                <FacebookIcon size={30} />
                                            )}
                                        </button>
                                    ))}
                            </div>
                        </div>
                    </div>
                </div>

                <div className="px-6 py-4 flex justify-center items-center border-t border-brand-gray-selection">
                    <div className="flex gap-2 text-gray-300 font-light text-sm items-center justify-center">
                        {copyright} <CopyrightIcon size={14} /> {year} – {companyName}
                    </div>
                </div>
            </div>
        </footer>
    )

    if (!isMounted) return null
    if (isMobile) return <MobileView />
    if (isTablet) return <TabletView />
    return <DesktopView />
}

export default Footer
