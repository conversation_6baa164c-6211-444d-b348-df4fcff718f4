import { NextRequest, NextResponse } from 'next/server'

// Configuration constants
const DEFAULT_REDIRECT = '/discovery'
const AUTH_REDIRECT = '/auth'

// Define protected routes that require authentication
const PROTECTED_ROUTES = [
    '/dashboard',
    '/profile',
    '/orders',
    '/cart',
    '/checkout',
    '/account',
    '/settings',
]

// Define auth routes that should redirect to home if already authenticated
const AUTH_ROUTES = ['/auth', '/auth/otp-verification']

// Public routes that don't need authentication checks
const PUBLIC_ROUTES = ['/', '/discovery', '/products', '/categories']

// Pre-compile route patterns for performance
const createRouteChecker = (routes: string[]) => {
    const patterns = routes.map((route) => ({
        exact: route,
        prefix: `${route}/`,
    }))

    return (pathname: string) =>
        patterns.some(({ exact, prefix }) => pathname === exact || pathname.startsWith(prefix))
}

const isProtectedRoute = createRouteChecker(PROTECTED_ROUTES)
const isAuthRoute = createRouteChecker(AUTH_ROUTES)

// Development logging helper
const log = (message: string) => {
    if (process.env.NODE_ENV === 'development') {
        console.log(`[Middleware] ${message}`)
    }
}

/**
 * Next.js middleware for authentication and route protection
 *
 * This middleware:
 * 1. Protects routes that require authentication
 * 2. Redirects authenticated users away from auth pages
 * 3. Handles token validation at the edge
 *
 * @param req - Next.js request object
 * @returns Next.js response
 */
export function middleware(req: NextRequest) {
    const { pathname } = req.nextUrl

    try {
        // Only trust server-side session token for security
        const sessionToken = req.cookies.get('session_token')
        const isAuthenticated = !!sessionToken?.value

        log(`Path: ${pathname}, Authenticated: ${isAuthenticated}`)

        // Check if this is a protected route
        if (isProtectedRoute(pathname) && !isAuthenticated) {
            const url = new URL(AUTH_REDIRECT, req.url)
            // Add the original URL as a query parameter to redirect back after login
            url.searchParams.set('callbackUrl', pathname)
            log(`Redirecting to: ${url.toString()}`)
            return NextResponse.redirect(url)
        }

        // Check if this is an auth route
        if (isAuthRoute(pathname) && isAuthenticated) {
            const redirectUrl = new URL(DEFAULT_REDIRECT, req.url)
            log(`Redirecting authenticated user to: ${redirectUrl.toString()}`)
            return NextResponse.redirect(redirectUrl)
        }

        // For all other routes, continue normally
        return NextResponse.next()
    } catch (error) {
        console.error('[Middleware] Error:', error)
        // Graceful fallback - continue to next middleware/page
        return NextResponse.next()
    }
}

// Configure middleware to run only on specific paths
export const config = {
    matcher: [
        /*
         * Match all request paths except:
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - public files (public folder)
         * - api routes (/api/*)
         */
        '/((?!_next/static|_next/image|favicon.ico|public|api).*)',
    ],
}
