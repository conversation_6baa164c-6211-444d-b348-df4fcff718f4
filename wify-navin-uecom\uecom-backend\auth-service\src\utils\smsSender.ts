import { CustomHttpService } from '../services/http';
import { SmsRequest } from '../types/auth.types';
import { ConfigService } from '../configs/config.service';
import { buildExtApiResponse } from './apiResponse';
import { APIResponse } from './../types/response.types';
import { StatusCodes } from 'http-status-codes';
import { smsRateLimiter } from '../services/redis-rate-limiter';

// SMS rate limiting configuration
const SMS_RATE_LIMITS = {
    // Maximum SMS per phone number in the time window
    maxPerPhone: 5,
    // Time window in seconds (15 minutes)
    timeWindowSeconds: 15 * 60,
};

export class SmsSender {
    private readonly httpService: CustomHttpService;
    private readonly config: ConfigService;

    constructor() {
        this.httpService = new CustomHttpService();
        this.config = new ConfigService();
    }

    /**
     * Validates SMS content for security and compliance
     * @param message - The SMS message to validate
     * @returns True if valid, false otherwise
     */
    private validateSmsContent(message: string): boolean {
        // Check if message is empty or too long (most SMS gateways have a limit)
        if (!message || message.trim().length === 0 || message.length > 160) {
            return false;
        }

        // Check for potentially malicious content (basic check)
        const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /data:/i,
            /onerror=/i,
            /onclick=/i,
        ];

        return !suspiciousPatterns.some((pattern) => pattern.test(message));
    }

    /**
     * Checks if a phone number has exceeded rate limits
     * @param phoneNumber - The phone number to check
     * @returns True if rate limited, false otherwise
     */
    private async isRateLimited(phoneNumber: string): Promise<boolean> {
        try {
            // Check if rate limited
            const isLimited = await smsRateLimiter.isRateLimited(
                phoneNumber,
                SMS_RATE_LIMITS.maxPerPhone,
                SMS_RATE_LIMITS.timeWindowSeconds
            );

            if (!isLimited) {
                // Increment counter if not limited
                await smsRateLimiter.increment(
                    phoneNumber,
                    SMS_RATE_LIMITS.timeWindowSeconds
                );
            }

            return isLimited;
        } catch (error) {
            console.error('❌ Error checking SMS rate limit:', error);
            return false; // Fail open if there's an error
        }
    }

    /**
     * Masks a phone number for logging purposes
     * @param phoneNumber - The phone number to mask
     * @returns Masked phone number (e.g., "******1234")
     */
    private maskPhoneNumber(phoneNumber: string): string {
        if (phoneNumber.length <= 4) {
            return '****';
        }
        return '*'.repeat(phoneNumber.length - 4) + phoneNumber.slice(-4);
    }

    /**
     * Sends an SMS using the configured mTalkz SMS gateway with enhanced security.
     * @param param0 - Object containing recipient phone number and message text.
     * @returns A structured API response indicating success or failure.
     */
    async sendSMS({ to, message }: SmsRequest): Promise<APIResponse> {
        try {
            // Input validation
            if (!to || typeof to !== 'string' || !/^\d{10,15}$/.test(to)) {
                return buildExtApiResponse(
                    StatusCodes.BAD_REQUEST,
                    'Invalid phone number format',
                    null,
                    { code: 'INVALID_PHONE_NUMBER' }
                );
            }

            if (!this.validateSmsContent(message)) {
                return buildExtApiResponse(
                    StatusCodes.BAD_REQUEST,
                    'Invalid SMS content',
                    null,
                    { code: 'INVALID_SMS_CONTENT' }
                );
            }

            // Extract the last 10 digits of the phone number (Indian mobile format)
            const phn = to.slice(-10);

            // Check rate limiting
            if (await this.isRateLimited(phn)) {
                return buildExtApiResponse(
                    StatusCodes.TOO_MANY_REQUESTS,
                    'Too many SMS requests. Please try again later.',
                    null,
                    { code: 'RATE_LIMIT_EXCEEDED' }
                );
            }

            // Retrieve SMS gateway configurations
            const apiKey = this.config.getMtalkzApiKey();
            const senderId = this.config.getMtalkzSenderId();

            // Check if API key is valid
            if (!apiKey || apiKey.trim() === '') {
                console.error('❌ API key is missing or empty!');
                throw new Error('API key is required for sending SMS');
            }

            // IMPORTANT: Try multiple possible API endpoints since the documentation might be outdated
            // Define possible API endpoints to try
            const possibleEndpoints = [
                {
                    baseUrl: 'https://msg.mtalkz.com',
                    subpath: '/V2/http-api-sms.php',
                }, // New endpoint from documentation
                {
                    baseUrl: 'https://msg.mtalkz.com',
                    subpath: '/V1/http-api.php',
                }, // Original endpoint
                {
                    baseUrl: 'https://api.mtalkz.com',
                    subpath: '/V2/http-api.php',
                },
                {
                    baseUrl: 'https://api.mtalkz.com',
                    subpath: '/v2/http-api.php',
                },
                {
                    baseUrl: 'https://api.mtalkz.com',
                    subpath: '/api/v2/sms/send',
                },
            ];

            // Use the first endpoint for now, we'll implement fallback logic later
            const { baseUrl, subpath } = possibleEndpoints[0];

            // Log the API URL being used
            console.log('📤 Using MTalkz API URL:', `${baseUrl}${subpath}`);

            // Properly encode each parameter individually to prevent injection
            const params = new URLSearchParams();
            params.append('apikey', apiKey);
            params.append('senderid', senderId);
            params.append('number', phn);
            params.append('message', message);

            // Construct the complete URL with properly encoded parameters
            const fullUrl = `${baseUrl}${subpath}?${params.toString()}`;

            // Log with masked phone number
            const maskedPhone = this.maskPhoneNumber(phn);
            console.log('📤 Sending SMS to:', maskedPhone);

            // Try different request methods and parameter formats
            let response: any;
            let responseData: any;
            let success = false;

            // Create a request body for POST requests
            const requestBody = {
                apikey: apiKey,
                senderid: senderId,
                number: phn,
                message: message,
            };

            // Log the API key being used (masked)
            const maskedApiKey =
                apiKey.substring(0, 4) +
                '...' +
                apiKey.substring(apiKey.length - 4);
            console.log('📤 Using API key:', maskedApiKey);

            // Try different approaches
            const attempts = [
                // Attempt 1: GET request with parameters in URL
                async () => {
                    console.log(
                        '📤 Attempt 1: GET request with parameters in URL'
                    );
                    return await this.httpService.get<any>(fullUrl);
                },
                // Attempt 2: POST request with parameters in URL
                async () => {
                    console.log(
                        '📤 Attempt 2: POST request with parameters in URL'
                    );
                    return await this.httpService.post<any>(fullUrl, null);
                },
                // Attempt 3: POST request with parameters in body as JSON
                async () => {
                    console.log(
                        '📤 Attempt 3: POST request with parameters in body as JSON'
                    );
                    const url = `${baseUrl}${subpath}`;
                    return await this.httpService.post<any>(url, requestBody, {
                        headers: { 'Content-Type': 'application/json' },
                    });
                },
                // Attempt 4: POST request with parameters in body as form data
                async () => {
                    console.log(
                        '📤 Attempt 4: POST request with parameters in body as form data'
                    );
                    const url = `${baseUrl}${subpath}`;
                    const formData = new URLSearchParams();
                    formData.append('apikey', apiKey);
                    formData.append('senderid', senderId);
                    formData.append('number', phn);
                    formData.append('message', message);
                    return await this.httpService.post<any>(
                        url,
                        formData.toString(),
                        {
                            headers: {
                                'Content-Type':
                                    'application/x-www-form-urlencoded',
                            },
                        }
                    );
                },
            ];

            // Try each attempt until one succeeds
            for (let i = 0; i < attempts.length; i++) {
                try {
                    response = await attempts[i]();
                    responseData = response?.data || response;

                    // Check if the response indicates success
                    if (
                        responseData &&
                        (responseData.Status === 'Success' ||
                            responseData.status === 'success' ||
                            responseData.type === 'success')
                    ) {
                        success = true;
                        console.log(`✅ Attempt ${i + 1} succeeded!`);
                        break;
                    }

                    // If we got a response but it's an error about the API key, break early
                    if (
                        responseData &&
                        (responseData.Status === 'Error' ||
                            responseData.status === 'error') &&
                        responseData.Details &&
                        responseData.Details.toLowerCase().includes('apikey')
                    ) {
                        console.log(
                            `⚠️ API key error detected in attempt ${
                                i + 1
                            }. Checking API key...`
                        );
                        break;
                    }

                    console.log(
                        `⚠️ Attempt ${
                            i + 1
                        } didn't return a success response, trying next method...`
                    );
                } catch (error: any) {
                    console.log(
                        `⚠️ Attempt ${i + 1} failed with error:`,
                        error.message || error
                    );
                }
            }

            // If all attempts failed with API key errors, log a specific message
            if (
                !success &&
                responseData &&
                (responseData.Status === 'Error' ||
                    responseData.status === 'error') &&
                responseData.Details &&
                responseData.Details.toLowerCase().includes('apikey')
            ) {
                console.log(
                    '❌ All attempts failed with API key errors. Please check your API key configuration.'
                );
            }

            // Log response without sensitive data
            const sanitizedResponse = { ...responseData };
            if (sanitizedResponse.data && sanitizedResponse.data.phoneNumber) {
                sanitizedResponse.data.phoneNumber = this.maskPhoneNumber(
                    sanitizedResponse.data.phoneNumber
                );
            }
            console.log(
                '✅ SMS Response:',
                JSON.stringify(sanitizedResponse, null, 2)
            );

            // Check if the SMS was sent successfully
            // The response format can vary between API versions:
            // V1: { "0": { "id": "1973060897-1", "mobile": "919022662592", "status": "SUBMITTED" } }
            // V2: { "status": "success", "data": { "id": "1973060897-1", ... } }

            // First, check for V2 API response format
            if (
                responseData &&
                (responseData.status === 'success' ||
                    responseData.type === 'success')
            ) {
                return buildExtApiResponse(
                    StatusCodes.OK,
                    responseData.message || 'SMS sent successfully',
                    responseData.data || responseData,
                    null
                );
            }

            // Then check for V1 API response format
            const hasValidResponse =
                responseData &&
                typeof responseData === 'object' &&
                Object.keys(responseData).length > 0;

            if (hasValidResponse) {
                // Get the first entry (usually with key "0")
                const firstKey = Object.keys(responseData)[0];
                const messageInfo = responseData[firstKey];

                // Check if the message has an ID and status
                if (messageInfo && messageInfo.id && messageInfo.status) {
                    // Consider "SUBMITTED" status as success
                    if (
                        messageInfo.status === 'SUBMITTED' ||
                        messageInfo.status === 'DELIVERED'
                    ) {
                        return buildExtApiResponse(
                            StatusCodes.OK,
                            'SMS sent successfully',
                            messageInfo,
                            null
                        );
                    }
                }
            }

            // If we reach here, the response format is unexpected but not necessarily an error
            console.warn(
                '⚠️ Unexpected SMS gateway response format:',
                responseData
            );

            // Try to determine if it's a success based on common patterns
            if (
                responseData &&
                // Check for common success indicators
                (responseData.success === true ||
                    responseData.status === 'success' ||
                    responseData.result === 'success' ||
                    (typeof responseData === 'string' &&
                        responseData.toLowerCase().includes('success')))
            ) {
                return buildExtApiResponse(
                    StatusCodes.OK,
                    'SMS likely sent successfully (based on response pattern)',
                    responseData,
                    null
                );
            }

            // Handle SMS gateway-level failure (non-success response)
            return buildExtApiResponse(
                StatusCodes.BAD_REQUEST,
                responseData?.message || 'SMS sending failed',
                null,
                {
                    code: 'SMS_GATEWAY_ERROR',
                    details: 'Invalid or unrecognized response format',
                }
            );
        } catch (error: any) {
            // Classify errors for better debugging and monitoring
            let errorCode = 'UNKNOWN_ERROR';
            let statusCode = StatusCodes.INTERNAL_SERVER_ERROR;

            if (
                error.code === 'ECONNREFUSED' ||
                error.code === 'ECONNABORTED'
            ) {
                errorCode = 'CONNECTION_ERROR';
            } else if (
                error.response?.status === 401 ||
                error.response?.status === 403
            ) {
                errorCode = 'AUTHENTICATION_ERROR';
                statusCode = StatusCodes.UNAUTHORIZED;
            } else if (error.response?.status === 429) {
                errorCode = 'RATE_LIMIT_ERROR';
                statusCode = StatusCodes.TOO_MANY_REQUESTS;
            }

            console.error('❌ Error sending SMS:', errorCode, error.message);

            // Handle any runtime/server-level error (like network issues)
            return buildExtApiResponse(statusCode, 'Error sending SMS', null, {
                code: errorCode,
                message: error.message,
            });
        }
    }
}

export const smsSender = new SmsSender();
