/**
 * Logger Utility
 *
 * This module provides a standardized logging interface for the application.
 * It supports different log levels and formats based on the environment.
 *
 * @module utils/logger
 */

/**
 * Log levels
 */
enum LogLevel {
    ERROR = 0,
    WARN = 1,
    INFO = 2,
    DEBUG = 3,
}

/**
 * Logger class
 */
class Logger {
    private level: LogLevel;
    private serviceName: string;

    /**
     * Creates a new Logger instance
     *
     * @param serviceName - Name of the service for log prefixing
     */
    constructor(serviceName: string = 'auth-service') {
        this.serviceName = serviceName;

        // Set log level based on environment
        const env = process.env.NODE_ENV || 'development';
        const logLevel =
            process.env.LOG_LEVEL || (env === 'production' ? 'info' : 'debug');

        switch (logLevel.toLowerCase()) {
            case 'error':
                this.level = LogLevel.ERROR;
                break;
            case 'warn':
                this.level = LogLevel.WARN;
                break;
            case 'info':
                this.level = LogLevel.INFO;
                break;
            case 'debug':
                this.level = LogLevel.DEBUG;
                break;
            default:
                this.level = LogLevel.INFO;
        }
    }

    /**
     * Formats a log message with timestamp, service name, and request ID
     *
     * @param level - Log level indicator
     * @param message - Log message
     * @param requestId - Optional request ID for tracing
     * @returns Formatted log message
     */
    private formatMessage(
        level: string,
        message: string,
        requestId?: string
    ): string {
        const timestamp = new Date().toISOString();
        const requestIdStr = requestId ? `[${requestId}] ` : '';
        return `[${timestamp}] [${this.serviceName}] [${level}] ${requestIdStr}${message}`;
    }

    /**
     * Sanitizes sensitive data from objects for logging
     *
     * @param data - Data to sanitize
     * @returns Sanitized data
     */
    private sanitize(data: any): any {
        if (!data || typeof data !== 'object') {
            return data;
        }

        // Clone the object to avoid modifying the original
        const sanitized = Array.isArray(data) ? [...data] : { ...data };

        // List of sensitive fields to mask
        const sensitiveFields = [
            'password',
            'token',
            'accessToken',
            'refreshToken',
            'jwt',
            'secret',
            'apiKey',
            'api_key',
            'key',
            'otp',
        ];

        // Mask sensitive fields
        for (const key in sanitized) {
            if (sensitiveFields.includes(key.toLowerCase())) {
                sanitized[key] = '********';
            } else if (
                typeof sanitized[key] === 'object' &&
                sanitized[key] !== null
            ) {
                sanitized[key] = this.sanitize(sanitized[key]);
            }
        }

        return sanitized;
    }

    /**
     * Logs an error message
     *
     * @param message - Log message
     * @param data - Additional data to log
     * @param requestId - Optional request ID for tracing
     */
    error(message: string, data?: any, requestId?: string): void {
        if (this.level >= LogLevel.ERROR) {
            console.error(
                this.formatMessage('ERROR', message, requestId),
                data ? this.sanitize(data) : ''
            );
        }
    }

    /**
     * Logs a warning message
     *
     * @param message - Log message
     * @param data - Additional data to log
     * @param requestId - Optional request ID for tracing
     */
    warn(message: string, data?: any, requestId?: string): void {
        if (this.level >= LogLevel.WARN) {
            console.warn(
                this.formatMessage('WARN', message, requestId),
                data ? this.sanitize(data) : ''
            );
        }
    }

    /**
     * Logs an info message
     *
     * @param message - Log message
     * @param data - Additional data to log
     * @param requestId - Optional request ID for tracing
     */
    info(message: string, data?: any, requestId?: string): void {
        if (this.level >= LogLevel.INFO) {
            console.info(
                this.formatMessage('INFO', message, requestId),
                data ? this.sanitize(data) : ''
            );
        }
    }

    /**
     * Logs a debug message
     *
     * @param message - Log message
     * @param data - Additional data to log
     * @param requestId - Optional request ID for tracing
     */
    debug(message: string, data?: any, requestId?: string): void {
        if (this.level >= LogLevel.DEBUG) {
            console.debug(
                this.formatMessage('DEBUG', message, requestId),
                data ? this.sanitize(data) : ''
            );
        }
    }
}

// Export a singleton instance
export const logger = new Logger();
