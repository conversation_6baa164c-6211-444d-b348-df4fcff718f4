import { SectionData, PageData } from '../types'

// Define layout service interface
export interface ILayoutService {
    getPageData(pageName: string): Promise<PageData>
    getSectionsByType(sections: SectionData[], type: string): SectionData[]
    getHeaderSection(sections: SectionData[]): SectionData | undefined
    getFooterSection(sections: SectionData[]): SectionData | undefined
}

/**
 * Data source interface for layout data
 */
export interface ILayoutDataSource {
    fetchPageData(pageName: string): Promise<PageData>
}

/**
 * API data source implementation
 * Fetches data from an API endpoint
 */
class ApiLayoutDataSource implements ILayoutDataSource {
    private apiBaseUrl: string

    constructor(apiBaseUrl: string) {
        this.apiBaseUrl = apiBaseUrl
    }

    async fetchPageData(pageName: string): Promise<PageData> {
        try {
            console.log(
                `ApiLayoutDataSource fetching from: ${this.apiBaseUrl}/api/page-data/${pageName}`,
            )

            const response = await fetch(`${this.apiBaseUrl}/api/page-data/${pageName}`)

            if (!response.ok) {
                throw new Error(`Failed to fetch page data: ${response.statusText}`)
            }

            const data = await response.json()

            // Validate the data structure
            if (!data || !Array.isArray(data.sections)) {
                console.warn('Invalid page data structure received from API:', data)
                return { sections: [] }
            }

            return data
        } catch (error) {
            console.error('Error fetching page data from API:', error)
            // Return empty page data as fallback
            return { sections: [] }
        }
    }
}

/**
 * Layout service implementation
 * Responsible for fetching and managing page layout data
 * Follows the Single Responsibility Principle by focusing only on layout concerns
 * Uses dependency injection for data source to allow for easy switching between mock and API data
 */
class LayoutService implements ILayoutService {
    private dataSource: ILayoutDataSource

    constructor(dataSource: ILayoutDataSource) {
        this.dataSource = dataSource
    }

    /**
     * Get page data by page name
     * Delegates to the data source for fetching
     *
     * @param pageName - Name of the page to fetch data for
     * @returns Promise resolving to page data
     */
    async getPageData(pageName: string): Promise<PageData> {
        try {
            const pageData = await this.dataSource.fetchPageData(pageName)

            // Validate the page data structure
            if (!pageData || !Array.isArray(pageData.sections)) {
                console.error('Invalid page data structure:', pageData)
                return { sections: [] }
            }

            return pageData
        } catch (error) {
            console.error('Error loading page data:', error)
            // Return empty page data as fallback
            return { sections: [] }
        }
    }

    /**
     * Get sections by type
     *
     * @param sections - Array of sections to filter
     * @param type - Section type to filter by
     * @returns Filtered array of sections
     */
    getSectionsByType(sections: SectionData[], type: string): SectionData[] {
        return sections.filter((section) => section.type === type)
    }

    /**
     * Get header section
     *
     * @param sections - Array of sections to search
     * @returns Header section or undefined if not found
     */
    getHeaderSection(sections: SectionData[]): SectionData | undefined {
        return sections.find((section) => section.type === 'header')
    }

    /**
     * Get footer section
     *
     * @param sections - Array of sections to search
     * @returns Footer section or undefined if not found
     */
    getFooterSection(sections: SectionData[]): SectionData | undefined {
        return sections.find((section) => section.type === 'footer')
    }
}

// Get the API base URL from environment variables or use a default
const API_BASE_URL = process.env.NEXT_PUBLIC_APP_SERVICE_URL || 'http://localhost:8000'
console.log(`LayoutService using API base URL: ${API_BASE_URL}`)

// Create API data source
const apiDataSource = new ApiLayoutDataSource(API_BASE_URL)

// Create and export a singleton instance with the API data source
export const layoutService = new LayoutService(apiDataSource)

// Export factory function for testing purposes
export const createApiLayoutService = () => new LayoutService(apiDataSource)
