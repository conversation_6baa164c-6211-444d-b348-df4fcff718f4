export interface OtpRequest {
    mobile: string;
}

export interface OtpVerificationRequest {
    mobile: string;
    otp: string;
}

export interface SmsRequest {
    to: string;
    message: string;
}

export interface ServiceResponse<T> {
    success: boolean;
    data?: T;
    error?: {
        message: string;
        code: string;
    };
}

export interface OtpServiceResponse {
    message: string;
    mobile: string;
    otp?: string;
}

export interface VerificationResponse {
    message: string;
}
