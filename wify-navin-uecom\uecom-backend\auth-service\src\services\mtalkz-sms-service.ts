/**
 * MTalkz SMS Service Implementation
 *
 * This class provides an implementation of the SMS service interface
 * using the MTalkz SMS gateway. It follows the Adapter pattern by adapting
 * the MTalkz API to our SMS service interface.
 *
 * @module services/mtalkz-sms-service
 */

import { ISmsService, SmsRequest } from '../interfaces/sms-service.interface';
import { smsSender } from '../utils/smsSender';
import { APIResponse } from '../types/response.types';

export class MtalkzSmsService implements ISmsService {
    /**
     * Sends an SMS message using the MTalkz SMS gateway
     *
     * @param {SmsRequest} request - The SMS request containing recipient and message
     * @returns {Promise<boolean>} True if the SMS was sent successfully, false otherwise
     */
    async sendSMS(request: SmsRequest): Promise<boolean> {
        const response: APIResponse = await smsSender.sendSMS(request);
        // Return true if the status code is OK (200), false otherwise
        return response.statusCode === 200;
    }
}

// Export a singleton instance
export const mtalkzSmsService = new MtalkzSmsService();
