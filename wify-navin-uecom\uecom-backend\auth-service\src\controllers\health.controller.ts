/**
 * Health Check Controller
 *
 * This controller provides endpoints for checking the health of the service
 * and its dependencies.
 *
 * @module controllers/health
 */

import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { buildAPIResponse } from '../utils/apiResponse';
import { container } from '../di/container';
import { SERVICE_TOKENS } from '../di/tokens';
import { IRedisRepository } from '../interfaces/redis-repository.interface';
import { logger } from '../utils/logger';
import os from 'os';
import { DatabaseHealthService } from '../services/database-health.service';

/**
 * Health status enum
 */
enum HealthStatus {
    UP = 'UP',
    DOWN = 'DOWN',
    DEGRADED = 'DEGRADED',
}

/**
 * Health check response interface
 */
interface HealthCheckResponse {
    status: HealthStatus;
    version: string;
    timestamp: string;
    uptime: number;
    host: string;
    dependencies: {
        redis: {
            status: HealthStatus;
            latency?: number;
            error?: string;
        };
        database: {
            status: HealthStatus;
            latency?: number;
            error?: string;
            circuitState?: string;
            failureCount?: number;
        };
        // Add other dependencies here as needed
    };
    memory: {
        total: number;
        free: number;
        used: number;
        usage: number;
    };
    cpu: {
        cores: number;
        load: number[];
    };
}

/**
 * Basic health check handler
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const basicHealthCheck = (_req: Request, res: Response): void => {
    const response = buildAPIResponse(StatusCodes.OK, 'Service is healthy', {
        status: HealthStatus.UP,
        timestamp: new Date().toISOString(),
    });

    res.status(StatusCodes.OK).json(response);
};

/**
 * Detailed health check handler
 *
 * @param req - Express request object
 * @param res - Express response object
 */
export const detailedHealthCheck = async (
    _req: Request,
    res: Response
): Promise<void> => {
    try {
        const startTime = Date.now();

        // Get Redis repository from container
        const redisRepository = container.resolve<IRedisRepository>(
            SERVICE_TOKENS.REDIS_REPOSITORY
        );

        // Get database health service from container
        const databaseHealthService = container.resolve<DatabaseHealthService>(
            SERVICE_TOKENS.DATABASE_HEALTH_SERVICE
        );

        // Check Redis connection
        let redisStatus: HealthStatus = HealthStatus.DOWN;
        let redisLatency: number | undefined;
        let redisError: string | undefined;
        let redisPerformance: 'good' | 'poor' = 'good'; // Track performance

        try {
            const redisStartTime = Date.now();
            const isRedisConnected = await redisRepository.isReady();
            redisLatency = Date.now() - redisStartTime;

            // Set status based on connection and latency
            if (isRedisConnected) {
                // If latency is high, mark as degraded
                if (redisLatency > 500) {
                    // 500ms threshold
                    redisStatus = HealthStatus.DEGRADED;
                    redisPerformance = 'poor';
                } else {
                    redisStatus = HealthStatus.UP;
                }
            } else {
                redisStatus = HealthStatus.DOWN;
            }
        } catch (error) {
            redisStatus = HealthStatus.DOWN;
            redisError =
                error instanceof Error ? error.message : 'Unknown error';
            logger.error('Redis health check failed', {
                error: error instanceof Error ? error.message : 'Unknown error',
            });
        }

        // Check database connection
        let dbStatus: HealthStatus = HealthStatus.DOWN;
        let dbLatency: number | undefined;
        let dbError: string | undefined;
        let dbCircuitState: string | undefined;
        let dbFailureCount: number | undefined;
        let dbPerformance: 'good' | 'poor' = 'good'; // Track performance

        try {
            // Get current database health status
            const dbHealthStatus = databaseHealthService.getStatus();

            if (dbHealthStatus) {
                dbLatency = dbHealthStatus.responseTime;
                dbCircuitState = dbHealthStatus.details?.circuitState;
                dbFailureCount = dbHealthStatus.details?.failureCount;

                // Set status based on health check result
                if (dbHealthStatus.status === 'UP') {
                    dbStatus = HealthStatus.UP;
                } else if (dbHealthStatus.status === 'DEGRADED') {
                    dbStatus = HealthStatus.DEGRADED;
                    dbPerformance = 'poor';
                } else {
                    dbStatus = HealthStatus.DOWN;
                    dbError = dbHealthStatus.details?.error;
                }
            } else {
                // If no health check has been performed yet, run one now
                const dbStartTime = Date.now();
                const dbHealthCheck = await databaseHealthService.checkHealth();
                dbLatency = Date.now() - dbStartTime;

                dbCircuitState = dbHealthCheck.details?.circuitState;
                dbFailureCount = dbHealthCheck.details?.failureCount;

                // Set status based on health check result
                if (dbHealthCheck.status === 'UP') {
                    dbStatus = HealthStatus.UP;
                } else if (dbHealthCheck.status === 'DEGRADED') {
                    dbStatus = HealthStatus.DEGRADED;
                    dbPerformance = 'poor';
                } else {
                    dbStatus = HealthStatus.DOWN;
                    dbError = dbHealthCheck.details?.error;
                }
            }
        } catch (error) {
            dbStatus = HealthStatus.DOWN;
            dbError = error instanceof Error ? error.message : 'Unknown error';
            logger.error('Database health check failed', {
                error: error instanceof Error ? error.message : 'Unknown error',
            });
        }

        // Determine overall status
        let overallStatus = HealthStatus.UP;

        // Check Redis status
        if (redisStatus === HealthStatus.DOWN) {
            overallStatus = HealthStatus.DOWN;
        } else if (redisPerformance === 'poor') {
            overallStatus = HealthStatus.DEGRADED;
        }

        // Check Database status
        if (dbStatus === HealthStatus.DOWN) {
            overallStatus = HealthStatus.DOWN;
        } else if (
            dbStatus === HealthStatus.DEGRADED &&
            overallStatus !== HealthStatus.DOWN
        ) {
            overallStatus = HealthStatus.DEGRADED;
        }

        // Get memory usage
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        const memoryUsage = Math.round((usedMemory / totalMemory) * 100) / 100;

        // Build health check response
        const healthCheck: HealthCheckResponse = {
            status: overallStatus,
            version: process.env.npm_package_version || '1.0.0',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            host: os.hostname(),
            dependencies: {
                redis: {
                    status: redisStatus,
                    latency: redisLatency,
                    error: redisError,
                },
                database: {
                    status: dbStatus,
                    latency: dbLatency,
                    error: dbError,
                    circuitState: dbCircuitState,
                    failureCount: dbFailureCount,
                },
            },
            memory: {
                total: totalMemory,
                free: freeMemory,
                used: usedMemory,
                usage: memoryUsage,
            },
            cpu: {
                cores: os.cpus().length,
                load: os.loadavg(),
            },
        };

        // Calculate response time
        const responseTime = Date.now() - startTime;

        // Log health check result
        logger.info(`Health check completed in ${responseTime}ms`, {
            status: overallStatus,
        });

        // Set appropriate status code based on health status
        let statusCode = StatusCodes.OK;

        if (overallStatus === HealthStatus.DOWN) {
            statusCode = StatusCodes.SERVICE_UNAVAILABLE;
        } else if (overallStatus === HealthStatus.DEGRADED) {
            statusCode = StatusCodes.OK; // Still return 200 for degraded but include the status
        }

        const response = buildAPIResponse(
            statusCode,
            `Service is ${overallStatus.toLowerCase()}`,
            healthCheck
        );

        res.status(statusCode).json(response);
    } catch (error) {
        logger.error('Health check failed', {
            error: error instanceof Error ? error.message : 'Unknown error',
        });

        const response = buildAPIResponse(
            StatusCodes.INTERNAL_SERVER_ERROR,
            'Health check failed',
            {
                status: HealthStatus.DOWN,
                timestamp: new Date().toISOString(),
                error: error instanceof Error ? error.message : 'Unknown error',
            }
        );

        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json(response);
    }
};
