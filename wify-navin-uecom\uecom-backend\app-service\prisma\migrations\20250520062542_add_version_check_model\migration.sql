-- CreateTable
CREATE TABLE "version_check" (
    "id" SERIAL NOT NULL,
    "platform" TEXT NOT NULL,
    "current_version" TEXT NOT NULL,
    "stable_version" TEXT NOT NULL,
    "stable_message" TEXT,
    "critical_message" TEXT,
    "stable_title" TEXT,
    "critical_title" TEXT,
    "download_url" TEXT NOT NULL,
    "is_playstore_immediate_release" BOOLEAN NOT NULL DEFAULT false,
    "version_helper_visibility" BOOLEAN NOT NULL DEFAULT true,
    "show_playstore_release" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "version_check_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "version_check_platform_key" ON "version_check"("platform");
