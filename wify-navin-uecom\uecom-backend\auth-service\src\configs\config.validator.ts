/**
 * Configuration Validator
 *
 * This module validates environment variables at startup to ensure all required
 * configuration is present and properly formatted.
 *
 * @module configs/config.validator
 */

/**
 * Environment variable configuration
 */
interface EnvConfig {
    name: string;
    required: boolean;
    default?: string;
    validator?: (value: string) => boolean;
    errorMessage?: string;
}

/**
 * Configuration schema for environment variables
 */
const configSchema: EnvConfig[] = [
    {
        name: 'PORT',
        required: false,
        default: '4000',
        validator: (value) => !isNaN(Number(value)),
        errorMessage: 'PORT must be a valid number',
    },
    {
        name: 'NODE_ENV',
        required: false,
        default: 'development',
        validator: (value) =>
            ['development', 'production', 'test'].includes(value),
        errorMessage: 'NODE_ENV must be one of: development, production, test',
    },
    {
        name: 'JWT_SECRET',
        required: true,
        validator: (value) => value.length >= 32,
        errorMessage: 'JWT_SECRET must be at least 32 characters long',
    },
    {
        name: 'ALLOWED_ORIGINS',
        required: false,
        default: 'http://localhost:3000',
        validator: (value) =>
            value.split(',').every((origin) => origin.startsWith('http')),
        errorMessage:
            'ALLOWED_ORIGINS must be a comma-separated list of valid URLs',
    },
    {
        name: 'REDIS_URL',
        required: true,
        validator: (value) => value.startsWith('redis://'),
        errorMessage:
            'REDIS_URL must be a valid Redis URL starting with redis://',
    },
    {
        name: 'REDIS_OTP_PREFIX',
        required: false,
        default: 'otp:',
    },
    {
        name: 'OTP_EXPIRATION_SECONDS',
        required: false,
        default: '900',
        validator: (value) => !isNaN(Number(value)) && Number(value) > 0,
        errorMessage: 'OTP_EXPIRATION_SECONDS must be a positive number',
    },
    {
        name: 'MTALKZ_API_KEY',
        required: true,
    },
    {
        name: 'MTALKZ_SENDER_ID',
        required: true,
        default: 'INWIFY',
    },
    {
        name: 'MSG_BASE_URL',
        required: true,
        validator: (value) => value.startsWith('http'),
        errorMessage: 'MSG_BASE_URL must be a valid URL',
    },
    {
        name: 'MSG_SUB_PATH',
        required: false,
        default: '/V2/http-api.php',
    },
    {
        name: 'DATABASE_URL',
        required: true,
        validator: (value) => value.startsWith('postgresql://'),
        errorMessage:
            'DATABASE_URL must be a valid PostgreSQL connection string',
    },
];

/**
 * Validates all environment variables against the schema
 *
 * @returns {string[]} Array of error messages, empty if all valid
 */
export const validateConfig = (): string[] => {
    const errors: string[] = [];

    for (const config of configSchema) {
        const value = process.env[config.name];

        // Check if required and present
        if (config.required && !value) {
            errors.push(
                `Missing required environment variable: ${config.name}`
            );
            continue;
        }

        // If value is present and has a validator, validate it
        if (value && config.validator && !config.validator(value)) {
            errors.push(
                config.errorMessage || `Invalid value for ${config.name}`
            );
        }

        // Set default value if not present
        if (!value && config.default) {
            process.env[config.name] = config.default;
        }
    }

    return errors;
};

/**
 * Validates configuration and exits if invalid
 */
export const validateConfigOrExit = (): void => {
    const errors = validateConfig();

    // In development mode, just warn about missing configuration
    if (process.env.NODE_ENV === 'development' && errors.length > 0) {
        console.warn('⚠️ Configuration validation warnings:');
        errors.forEach((error) => console.warn(`  - ${error}`));
        console.warn(
            'These issues should be fixed before deploying to production.'
        );
        return;
    }

    // In production mode, exit if there are errors
    if (errors.length > 0) {
        console.error('❌ Configuration validation failed:');
        errors.forEach((error) => console.error(`  - ${error}`));
        console.error('Please fix these issues and restart the application.');
        process.exit(1);
    }

    console.log('✅ Configuration validation passed');
};
